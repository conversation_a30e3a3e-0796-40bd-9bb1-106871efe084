热修复能力建设@王栋 
相关名词解释（类加载、底层替换、InstanRun等等）参考：Android热修复预研
目前存在的问题
App出现线上事故后，例如紧急的隐私合规问题或严重的用户体验问题，如果是原生代码导致，必须重新发版进行修复，存在以下几个问题：
- 重新发布版本需要较长的流程代价太大：修复代码→打包APK→自测、提测验证→增量发版
- 影响用户口碑。用户重新下载安装更新成本太高，用户不愿意频繁更新应用，尤其需要下载完整APK时。
-  响应不及时。发版流程涉及应用商店审核，需要更多的时间才能被在用户端生效，不能以最快的速度解决问题，紧急问题延误上线可能导致更大的影响。
动态更新部分小范围的代码逻辑或者UI调整（尤其是支持资源、so库、flutter也是so库替换的）
-  版本功能过于简单（小范围业务逻辑、UI调整）走完开发→提测→上线整个流程得不偿失
行业解决方案
代码修复：
  分层修复策略
    - 紧急修复：采用底层替换方案（如 Sophix 即时生效模式）快速止损实时生效，优先处理崩溃类问题。
    - 冷启动修复：使用类加载（如 Tinker）、Instant Run（美团Robust、Robust2.0） 方案下次冷启动修复，配合版本发布，逐步修复逻辑问题。
  混合方案：部分大厂（如Sophix）结合冷启动修复与实时补丁，提升整体成功率至 95% 以上。
  flutter热修复：sophix、tinker官方没有找到技术文档，但是github有人提出可以使用https://github.com/magicbaby810/HotfixFlutter
资源修复：
  目前市面上大部分资源热修复方案基本都参考了Instant Run的实现，flutter资源也是放在assets下的flutter_assets目录因此支持。 其主要分两步：
  1. 创建新的AssetManager，并通过反射调用addAssetPath加载完整的新资源包
  2. 找到所有之前引用到原有AssetManager的地方，通过反射，把引用处 替换为新AssetManager
  - 这里的具体原理可以参考章探索Android开源框架 - 10. 插件化原理中的资源加载部分；
  - Sophix: 构造了一个package id为0x66的资源包(原有资源包为 0x7f），此包只包含改变了的资源项，然后直接在原有的AssetManager中 addAssetPath这个包就可以了，不修改AssetManager的引用处，替换更快更安全
so库修复
  主要就是如下两种方案：
  - 将so补丁插入到NativeLibraryElement数组的前部，让so补丁的路径先被返回和加载；
  - 调用System.load方法来接管系统so的加载入口；
方案
推荐方案：Sophix 收费版（优先选择
核心优势：
1. 综合性能最优：冷启动时间增幅仅 0.03%-21.7%（小米 10 至小米 Max3），内存 / CPU 占用稳定。
2. 全类型修复支持：同时支持 dex、资源、so 库热修复，且资源修复采用差量包机制，无需合成。
3. 接入成本低：一键集成，自动处理补丁下载、合并，无需自定义 Gradle 配置（Tinker虽然免费但是需要各种修改gradle进行开发且性能对比差很多）。
4. 服务保障：Sophix持续更新维护，提供技术支持，之前提过高版本gradle问题，后期已经修复。
备选方案：Tinker 开源版（无费用场景）
核心优势：
免费
接入挑战：
- 手动配置 Gradle 参数（如 oldApk、newApk 路径），参考官方 Sample 处理 ApplicationLike 继承。
- 需单独处理 so 库加载（调用 TinkerLoadLibrary.installNativeLibraryABI）。
- 性能较差，小米MAX3实测资源修复冷启动需要180s
- 不支持实时修复，需要下次冷启动生效  