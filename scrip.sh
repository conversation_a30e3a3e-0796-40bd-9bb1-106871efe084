#!/bin/bash




# 获取传递的参数
param1="$1"  # 第一个参数

# 根据 param1 的值设置 URL 参数
if [[ "$param1" == "2" ]]; then
    url_parameter="past1days"
else
    url_parameter="thisday"
fi



# 封装方法 B
function method_B {
    local messages="$1"

            #local webhook_url="https://open.feishu.cn/open-apis/bot/v2/hook/d0f5a2bb-5bb2-4368-80cb-acf092c6ae87"
            local webhook_url="https://open.feishu.cn/open-apis/bot/v2/hook/3dc5cc4f-68af-4437-86da-bc6cedc535eb"


    # 获取当前日期
    current_date=$(date +"%Y-%m-%d")

    # 判断是否为昨日并获取昨天的日期
    if [[ "$url_parameter" == "past1days" ]]; then
        current_date2=$(date -v -1d +"%Y-%m-%d")
        today="昨日${current_date2}"
    else
        today="今日${current_date}"
    fi

    # 获取当前时间
    current_time=$(date +"%H:%M:%S")
    today="${today} 查询时间: ${current_date} ${current_time}"


    local message="{\"msg_type\":\"interactive\",\"card\":{\"elements\":[{\"tag\":\"div\",\"text\":{\"content\":\"${messages}\",\"tag\":\"lark_md\"}},{\"actions\":[{\"tag\":\"button\",\"text\":{\"content\":\"修正工时请移步Z·ONE\",\"tag\":\"lark_md\"},\"url\":\"https://zone.haier.net:8080/#/home/<USER>",\"type\":\"default\",\"value\":{}}],\"tag\":\"action\"}],\"header\":{\"title\":{\"content\":\"${today}\",\"tag\":\"plain_text\"}}}}"

    # 构建 message2
    local message2="{\"msg_type\":\"text\",\"content\":{\"text\":\"<at user_id=\\\"all\\\"></at> 工时登记异常提醒\"}}"
    result=$(curl -s -X POST -H "Content-Type: application/json" -d "$message2" "$webhook_url")
    result=$(curl -s -X POST -H "Content-Type: application/json" -d "$message" "$webhook_url")
    echo "执行结果: $result"
}



# 基础 URL
base_url="https://metabase.haier.net/api/public/dashboard/e48e4958-4d63-43ff-b303-e73ba05d5621/dashcard/432/card/459"

json_data=$(jq -n --arg param "$url_parameter" '[
    {
        "type": "string/=",
        "value": ["APP架构", "Android架构", "iOS架构"],
        "id": "c343c339",
        "target": ["dimension", ["template-tag", "team"]]
    },
    {
        "type": "date/all-options",
        "value": $param,
        "id": "2c072968",
        "target": ["dimension", ["template-tag", "selectdate"]]
    }
]')

# URL 编码
encoded_parameters=$(printf "%s" "$json_data" | jq -sRr @uri)

# 构造完整的 URL
url="$base_url?parameters=$encoded_parameters"



# 发起 GET 请求并解析 JSON 响应
response=$(curl -s -w "%{http_code}" -o temp_response.json --location -g --request GET "$url" \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: metabase.haier.net' \
--header 'Connection: keep-alive' \
--header 'Cookie: metabase.DEVICE=8688dfb7-ddfd-4f8b-92f4-55ad150df8ac')

status_code=$response

# 读取响应内容
response_content=$(<temp_response.json)


# 检查状态码
if [[ "$status_code" -ne 200 && "$status_code" -ne 202 ]]; then
    echo "请求失败，状态码: $status_code"
    exit 1
fi

# 使用 jq 工具解析 JSON 数据
rows=$(echo "$response_content" | jq -r '.data.rows')

# 检查 rows 是否为空
if [[ -z "$rows" ]]; then
    echo "没有返回任何数据。"
    exit 1
fi

# 用于拼接消息的变量
messages=""

# 遍历每一行
for row in $(echo "$rows" | jq -c '.[]'); do
    name=$(echo "$row" | jq -r '.[0]')
    value=$(echo "$row" | jq -r '.[2]')

    # 检查值是否小于 8
    if (( $(echo "$value < 8" | bc -l) )); then
        messages+="$name 实际工时为：【${value} 小时】\n"
    fi
done


# 发送消息
if [[ -n "$messages" ]]; then

    echo "执行结果: $messages"
    method_B "$messages"
else
    echo "没有符合条件的数据。"
    messages="无"
    method_B "$messages"
fi




