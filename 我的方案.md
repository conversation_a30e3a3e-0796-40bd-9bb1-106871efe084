
原理：
如果主线程超过系统规定的时间无响应，就会被 Watchdog 杀掉。这时，崩溃问题对应的异常编码是 0x8badf00d。 苹果文档

一些被系统杀掉的情况，可以通过异常编码来分析。在维基百科上，查看完整的异常编码。这里列出了 44 种异常编码，但常见的就是如下三种：
0x8badf00d，表示 App 在一定时间内无响应而被 watchdog 杀掉的情况。
0xdeadfa11，表示 App 被用户强制退出。
0xc00010ff，表示 App 因为运行造成设备温度太高而被杀掉。

利用 RunLoop 原理去监控卡顿
触发卡顿的时间阈值，我们可以根据 WatchDog 机制来设置。WatchDog 在不同状态下设置的不同时间，
如下所示： 来自极客时间
启动（Launch）：20s；
恢复（Resume）：10s；
挂起（Suspend）：10s；
退出（Quit）：6s；
后台（Background）：3min（每次申请 3min，可连续申请，最多申请到 10min）。
通过 WatchDog 设置的时间，可以把启动的阈值设置为 10 秒，其他状态则都默认设置为 3 秒。总的原则就是，要小于 WatchDog 的限制时间。当然了，这个阈值也不用小得太多，原则就是要优先解决用户感知最明显的体验问题。

方案实施
此功能需要一个开关控制是否启用。
1. 监控时机
@interface TMWatchDog : NSObject

@property (nonatomic, assign) NSInteger mIndex;
+ (instancetype)shareInstance;

- (void)beginMonitor; //开始监视卡顿
- (void)endMonitor;   //停止监视卡顿

@end

@interface TMWatchDog () {
    int timeoutCount;
    CFRunLoopObserverRef runLoopObserver;
    @public
    dispatch_semaphore_t dispatchSemaphore;
    CFRunLoopActivity runLoopActivity;
}

@end

@implementation TMWatchDog

+ (instancetype)shareInstance {
    static id instance = nil;
    static dispatch_once_t dispatchOnce;
    dispatch_once(&dispatchOnce, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)beginMonitor {
    //监测卡顿
    if (runLoopObserver) {
        return;
    }
    dispatchSemaphore = dispatch_semaphore_create(0); //Dispatch Semaphore保证同步
    //创建一个观察者
    CFRunLoopObserverContext context = {0,(__bridge void*)self,NULL,NULL};
    runLoopObserver = CFRunLoopObserverCreate(kCFAllocatorDefault,
                                              kCFRunLoopAllActivities,
                                              YES,
                                              0,
                                              &runLoopObserverCallBack,
                                              &context);
    //将观察者添加到主线程runloop的common模式下的观察中
    CFRunLoopAddObserver(CFRunLoopGetMain(), runLoopObserver, kCFRunLoopCommonModes);
    
    //创建子线程监控
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        //子线程开启一个持续的loop用来进行监控
        while (YES) {
            long semaphoreWait = dispatch_semaphore_wait(dispatchSemaphore, dispatch_time(DISPATCH_TIME_NOW, 3 * NSEC_PER_SEC));
            if (semaphoreWait != 0) {
                if (!runLoopObserver) {
                    dispatchSemaphore = 0;
                    runLoopActivity = 0;
                    return;
                }
                //两个runloop的状态，BeforeSources和AfterWaiting这两个状态区间时间能够检测到是否卡顿
               if (runLoopActivity == kCFRunLoopBeforeSources || runLoopActivity == kCFRunLoopAfterWaiting) {

                    dispatch_async(dispatch_get_global_queue(0, 0), ^{
                       NSLog(@"🚫 ⚠️ [Watchdog] main thread blocked");
                       [UPCrashReportManager reportUserException:@"TM_TEST" reason:@"TM_WATCH_DOG" stackTrace:nil];
                    });
               }
            }// end semaphore wait
        }// end while
    });
}


- (void)endMonitor {
    if (!runLoopObserver) {
        return;
    }
    CFRunLoopRemoveObserver(CFRunLoopGetMain(), runLoopObserver, kCFRunLoopCommonModes);
    CFRelease(runLoopObserver);
    runLoopObserver = NULL;
}

static void runLoopObserverCallBack(CFRunLoopObserverRef observer, CFRunLoopActivity activity, void *info){
    SMLagMonitor *lagMonitor = (__bridge SMLagMonitor*)info;
    lagMonitor->runLoopActivity = activity;
    
    dispatch_semaphore_t semaphore = lagMonitor->dispatchSemaphore;
    dispatch_semaphore_signal(semaphore);
}



@end

在 runLoop 状态切换时（卡顿），采集和上传崩溃日志。（ //两个runloop的状态，BeforeSources和AfterWaiting这两个状态区间时间能够检测到是否卡顿）
卡顿时长 目前设置为3秒；（卡顿大于3秒认为卡顿）

2. 崩溃日志采集
使用UPCrash 采集长传崩溃日志
/// 生成自定义异常类型的报告，存储在沙盒目录Document的customexceptionreport目录下
/// @param name 自定义异常名称，必填
/// @param reason 自定义异常原因，必填
/// @param stackTrace 自定义异常堆栈，非必填
+ (void)reportUserException:(NSString *)name
                     reason:(NSString *)reason
                 stackTrace:(NSArray *)stackTrace;
3. 验证
 通过点击按钮写了一个while 循环，卡住主线程。退到后台 App被杀死。左边为上传的日志，右边为手机系统日志。虽然左边没有符号化（debug包），但是对比行号一致。
[图片]
