[2025-06-26 18:46:00] [INFO] 脚本开始执行，参数: 
[2025-06-26 18:46:00] [DEBUG] 当前工作目录: /Users/<USER>/Desktop/gongshi
[2025-06-26 18:46:00] [DEBUG] 脚本所在目录: /Users/<USER>/Desktop/gongshi
[2025-06-26 18:46:00] [DEBUG] 当前用户: yanzhen
[2025-06-26 18:46:00] [DEBUG] PATH环境变量: /usr/local/bin:/opt/homebrew/bin:/usr/bin:/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin
[2025-06-26 18:46:00] [INFO] 检查依赖工具...
[2025-06-26 18:46:00] [INFO] 依赖工具检查完成
[2025-06-26 18:46:00] [INFO] 测试网络连接...
[2025-06-26 18:46:00] [INFO] 网络连接正常，HTTP状态码: 200
[2025-06-26 18:46:00] [INFO] 查询模式: 今日工时
[2025-06-26 18:46:00] [INFO] 开始构建数据查询请求...
[2025-06-26 18:46:00] [DEBUG] 查询参数: [
  {
    "type": "string/=",
    "value": [
      "APP架构",
      "Android架构",
      "iOS架构"
    ],
    "id": "c343c339",
    "target": [
      "dimension",
      [
        "template-tag",
        "team"
      ]
    ]
  },
  {
    "type": "date/all-options",
    "value": "thisday",
    "id": "2c072968",
    "target": [
      "dimension",
      [
        "template-tag",
        "selectdate"
      ]
    ]
  }
]
[2025-06-26 18:46:00] [DEBUG] 请求URL: https://metabase.haier.net/api/public/dashboard/e48e4958-4d63-43ff-b303-e73ba05d5621/dashcard/432/card/459?parameters=%5B%0A%20%20%7B%0A%20%20%20%20%22type%22%3A%20%22string%2F%3D%22%2C%0A%20%20%20%20%22value%22%3A%20%5B%0A%20%20%20%20%20%20%22APP%E6%9E%B6%E6%9E%84%22%2C%0A%20%20%20%20%20%20%22Android%E6%9E%B6%E6%9E%84%22%2C%0A%20%20%20%20%20%20%22iOS%E6%9E%B6%E6%9E%84%22%0A%20%20%20%20%5D%2C%0A%20%20%20%20%22id%22%3A%20%22c343c339%22%2C%0A%20%20%20%20%22target%22%3A%20%5B%0A%20%20%20%20%20%20%22dimension%22%2C%0A%20%20%20%20%20%20%5B%0A%20%20%20%20%20%20%20%20%22template-tag%22%2C%0A%20%20%20%20%20%20%20%20%22team%22%0A%20%20%20%20%20%20%5D%0A%20%20%20%20%5D%0A%20%20%7D%2C%0A%20%20%7B%0A%20%20%20%20%22type%22%3A%20%22date%2Fall-options%22%2C%0A%20%20%20%20%22value%22%3A%20%22thisday%22%2C%0A%20%20%20%20%22id%22%3A%20%222c072968%22%2C%0A%20%20%20%20%22target%22%3A%20%5B%0A%20%20%20%20%20%20%22dimension%22%2C%0A%20%20%20%20%20%20%5B%0A%20%20%20%20%20%20%20%20%22template-tag%22%2C%0A%20%20%20%20%20%20%20%20%22selectdate%22%0A%20%20%20%20%20%20%5D%0A%20%20%20%20%5D%0A%20%20%7D%0A%5D
[2025-06-26 18:46:00] [INFO] 开始请求工时数据...
[2025-06-26 18:46:01] [INFO] 数据请求完成，HTTP状态码: 202
[2025-06-26 18:46:01] [DEBUG] 响应内容长度:     1485 字符
[2025-06-26 18:46:01] [INFO] 数据请求成功，开始解析数据...
[2025-06-26 18:46:01] [INFO] 获取到 15 条工时记录
[2025-06-26 18:46:01] [INFO] 开始分析工时数据...
[2025-06-26 18:46:01] [INFO] 发现异常工时: 逄腾飞 = 0.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 闫振 = 0.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 王栋 = 0.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 陈正烽 = 0.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 马俊岭 = 0.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 高雯雯 = 2.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 路标 = 5.0 小时
[2025-06-26 18:46:01] [INFO] 发现异常工时: 吴洋漾 = 5.0 小时
[2025-06-26 18:46:01] [INFO] 数据分析完成，发现 8 条异常工时记录
[2025-06-26 18:46:01] [INFO] 准备发送异常工时提醒消息
[2025-06-26 18:46:01] [DEBUG] 消息内容: 逄腾飞 实际工时为：【0.0 小时】\n闫振 实际工时为：【0.0 小时】\n王栋 实际工时为：【0.0 小时】\n陈正烽 实际工时为：【0.0 小时】\n马俊岭 实际工时为：【0.0 小时】\n高雯雯 实际工时为：【2.0 小时】\n路标 实际工时为：【5.0 小时】\n吴洋漾 实际工时为：【5.0 小时】\n
[2025-06-26 18:46:01] [DEBUG] 异常用户列表: 逄腾飞
闫振
王栋
陈正烽
马俊岭
高雯雯
路标
吴洋漾
[2025-06-26 18:46:01] [INFO] 开始执行消息发送流程...
[2025-06-26 18:46:01] [INFO] 开始获取飞书访问令牌...
[2025-06-26 18:46:01] [DEBUG] 请求URL: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
[2025-06-26 18:46:01] [DEBUG] APP_ID: cli_a8da644c38fed013
[2025-06-26 18:46:01] [DEBUG] 认证请求数据: {
        "app_id": "cli_a8da644c38fed013",
        "app_secret": "vgdZOTcXh4rbG98XKc8vSdcrRJ3NfMnr"
    }
[2025-06-26 18:46:02] [DEBUG] HTTP状态码: 200
[2025-06-26 18:46:02] [DEBUG] 认证接口响应: {"code":0,"expire":7200,"msg":"ok","tenant_access_token":"t-g1046qiKWJIFPTQ3ENNA7WYWUPNXBQEUHA66L3SE"}
[2025-06-26 18:46:02] [INFO] 访问令牌获取成功，有效期: 7200秒
[2025-06-26 18:46:02] [DEBUG] Token: t-g1046qiKWJIFPTQ3EN...
[2025-06-26 18:46:02] [INFO] 准备发送消息，标题: 今日2025-06-26 查询时间: 2025-06-26 18:46:02
[2025-06-26 18:46:02] [INFO] 发送第1条消息: 工时登记异常提醒
[2025-06-26 18:46:02] [DEBUG] 第1条消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}
[2025-06-26 18:46:02] [INFO] 开始发送飞书消息...
[2025-06-26 18:46:02] [DEBUG] 消息类型: text
[2025-06-26 18:46:02] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-06-26 18:46:02] [DEBUG] 消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}...
[2025-06-26 18:46:02] [DEBUG] Token前缀: t-g1046qiKWJIFPTQ3EN...
[2025-06-26 18:46:02] [DEBUG] 完整请求数据: {
  "content": "{\"text\":\"<at user_id=\\\"all\\\"></at> 工时登记异常提醒\"}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "57b450eedffdd961ab0a18ea4fee84a9"
}
[2025-06-26 18:46:02] [DEBUG] HTTP状态码: 200
[2025-06-26 18:46:02] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"@_all 工时登记异常提醒\"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1750934762783","deleted":false,"message_id":"om_x100b4ae60067c8900f406ca903c5238","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1750934762783","updated":false},"msg":"success"}
[2025-06-26 18:46:02] [DEBUG] 解析结果 - code字段: '0'
[2025-06-26 18:46:02] [INFO] 消息发送成功，消息ID: om_x100b4ae60067c8900f406ca903c5238
[2025-06-26 18:46:02] [INFO] 消息UUID: 57b450eedffdd961ab0a18ea4fee84a9 (用于防重复)
[2025-06-26 18:46:02] [DEBUG] 业务状态码: 0 (成功)
[2025-06-26 18:46:02] [INFO] 第1条消息发送成功，message_id: om_x100b4ae60067c8900f406ca903c5238
[2025-06-26 18:46:02] [INFO] 准备发送第2条消息...
[2025-06-26 18:46:04] [DEBUG] 详细消息内容: {
  "text": "今日2025-06-26 查询时间: 2025-06-26 18:46:02\n\n逄腾飞 实际工时为：【0.0 小时】\n闫振 实际工时为：【0.0 小时】\n王栋 实际工时为：【0.0 小时】\n陈正烽 实际工时为：【0.0 小时】\n马俊岭 实际工时为：【0.0 小时】\n高雯雯 实际工时为：【2.0 小时】\n路标 实际工时为：【5.0 小时】\n吴洋漾 实际工时为：【5.0 小时】\n\n\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"
}
[2025-06-26 18:46:04] [INFO] 发送第2条消息: 详细工时信息
[2025-06-26 18:46:04] [INFO] 开始发送飞书消息...
[2025-06-26 18:46:04] [DEBUG] 消息类型: text
[2025-06-26 18:46:04] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-06-26 18:46:04] [DEBUG] 消息内容: {
  "text": "今日2025-06-26 查询时间: 2025-06-26 18:46:02\n\n逄腾飞 实际工时为：【0.0 小时】\n闫振 实际工时为：【0.0 小时】\n王栋 实际工...
[2025-06-26 18:46:04] [DEBUG] Token前缀: t-g1046qiKWJIFPTQ3EN...
[2025-06-26 18:46:04] [DEBUG] 完整请求数据: {
  "content": "{\n  \"text\": \"今日2025-06-26 查询时间: 2025-06-26 18:46:02\\n\\n逄腾飞 实际工时为：【0.0 小时】\\n闫振 实际工时为：【0.0 小时】\\n王栋 实际工时为：【0.0 小时】\\n陈正烽 实际工时为：【0.0 小时】\\n马俊岭 实际工时为：【0.0 小时】\\n高雯雯 实际工时为：【2.0 小时】\\n路标 实际工时为：【5.0 小时】\\n吴洋漾 实际工时为：【5.0 小时】\\n\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"\n}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "f68758b82a5e6bf47b1b3f25ddb9502d"
}
[2025-06-26 18:46:04] [DEBUG] HTTP状态码: 200
[2025-06-26 18:46:04] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"今日2025-06-26 查询时间: 2025-06-26 18:46:02\\n\\n逄腾飞 实际工时为：【0.0 小时】\\n闫振 实际工时为：【0.0 小时】\\n王栋 实际工时为：【0.0 小时】\\n陈正烽 实际工时为：【0.0 小时】\\n马俊岭 实际工时为：【0.0 小时】\\n高雯雯 实际工时为：【2.0 小时】\\n路标 实际工时为：【5.0 小时】\\n吴洋漾 实际工时为：【5.0 小时】\\n\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1750934764583","deleted":false,"message_id":"om_x100b4ae6000ad8900f45c382d362144","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1750934764583","updated":false},"msg":"success"}
[2025-06-26 18:46:04] [DEBUG] 解析结果 - code字段: '0'
[2025-06-26 18:46:04] [INFO] 消息发送成功，消息ID: om_x100b4ae6000ad8900f45c382d362144
[2025-06-26 18:46:04] [INFO] 消息UUID: f68758b82a5e6bf47b1b3f25ddb9502d (用于防重复)
[2025-06-26 18:46:04] [DEBUG] 业务状态码: 0 (成功)
[2025-06-26 18:46:04] [INFO] 第2条消息发送成功，message_id: om_x100b4ae6000ad8900f45c382d362144
[2025-06-26 18:46:04] [INFO] 当前时间(4 18:46)不在紧急短信接口调用时间范围内，跳过调用
[2025-06-26 18:46:04] [INFO] 调用条件：查询昨日工时 且 周二至周六早上8:20-8:45之间
[2025-06-26 18:46:04] [INFO] 当前查询模式: thisday, 当前星期: , 当前时间: 4 18:46
[2025-06-26 18:46:04] [INFO] 所有消息发送完成
[2025-06-26 18:46:04] [INFO] 脚本执行完成 - 发现异常工时
[2025-06-26 18:46:04] [DEBUG] 已清理临时文件
