# 行业大厂 iOS Watchdog 崩溃采集方案总结

## 1. 字节跳动 - 卡死崩溃监控方案

**原文链接**: https://blog.wyan.vip/2023/06/iOS_SIGKILL.html

### 1.1 Watchdog崩溃定义
- **崩溃类型**: `0x8badf00d` (ate bad food)
- **触发机制**: App在启动、退出或响应系统事件时耗时过长，触发系统保护机制
- **系统行为**: 进程被SIGKILL强制结束，无法在App内捕获

### 1.2 核心监控原理

#### 基于RunLoop的卡死检测
```mermaid
graph TD
    A[注册RunLoop Observer] --> B[监听线程等待信号]
    B --> C{超时8秒?}
    C -->|否| D[收到RunLoop状态变化]
    C -->|是| E[记录全线程堆栈到数据库]
    D --> F[重置超时计数]
    F --> B
    E --> G[每1秒采样更新卡死时间]
    G --> H{RunLoop进入下一活跃状态?}
    H -->|是| I[删除数据库记录]
    H -->|否| J[App被系统杀死]
    J --> K[下次启动检测并上报]
```

#### 卡死判定逻辑
1. **怀疑阶段**: 长时间卡顿被检测到，记录堆栈到数据库
2. **排除阶段**: 如果RunLoop进入下一活跃状态，删除记录
3. **确认阶段**: 下次启动检测到未删除记录，确认为卡死崩溃

### 1.3 技术优化方案

#### 多段等待解决挂起误报
- **问题**: App后台挂起导致的时间计算误差
- **方案**: 将8秒等待细分为8个1秒间隔等待
- **效果**: 挂起造成的误差不超过1秒

#### 主线程堆栈时间线
- **采集策略**: 超过阈值后每1秒采集主线程堆栈
- **数据保留**: 最多保留最近10次主线程调用栈
- **分析价值**: 提供主线程随时间变化的执行轨迹

### 1.4 常见Watchdog问题类型

#### 多线程死锁
- **典型场景**: dispatch_once中子线程同步访问主线程
- **案例**: CTTelephonyNetworkInfo初始化导致的死锁

#### 锁竞争问题
- **典型场景**: 主线程与子线程耗时操作存在锁竞争
- **案例**: YYDiskCache的信号量锁竞争

#### 磁盘IO密集
- **典型场景**: 主线程压缩/解压缩、数据库操作
- **影响**: 低端设备更容易触发

#### 跨进程通信
- **典型场景**: UIPasteBoard、NSUserDefaults、openURL
- **风险**: 其他进程异常导致当前App卡死

#### OC Runtime Lock死锁
- **典型场景**: dyld lock与OC runtime lock互相等待
- **解决**: 监控模块使用C/C++实现，避免OC依赖

---

## 2. 货拉拉 - MetricKit Abort异常监控

**原文链接**: https://juejin.cn/post/7212622837064368165

### 2.1 技术方案选择

#### 业界方案对比
| 方案 | 优点 | 缺点 | 实现难度 |
|------|------|------|----------|
| **手淘分支法** | 信息全面 | 日志量大，分析复杂 | 高 |
| **字节对症下药法** | 精准定位 | 自研成本高 | 极高 |
| **Flag标记法** | 简单易用 | 缺少堆栈信息 | 低 |
| **Apple MetricKit** | 官方支持 | 信息缺失，存在漏报 | 中 |

### 2.2 MetricKit实现方案

#### 核心流程
```mermaid
graph TD
    A[订阅MetricKit] --> B[接收Diagnostic数据]
    B --> C[数据加工处理]
    C --> D[补充缺失信息]
    D --> E[堆栈格式化]
    E --> F[上报到监控平台]

    G[修复iOS16兼容性] --> A
    H[封装WPFMetricKitManager] --> A
```

#### 关键技术点

**1. iOS 16兼容性修复**
```objc
// 修复iOS 16.0.1、16.0.2的系统缺陷
// 多个订阅者同时读写导致的crash
Exception: NSGenericException
Reason: Collection was mutated while being enumerated
```

**2. 数据补充策略**
- **崩溃时间**: 通过payload.timeStampBegin补充
- **设备信息**: 补充deviceId、系统版本等
- **应用信息**: 补充版本号、渠道等

**3. 堆栈格式化**
```objc
// iOS 16系统offsetIntoBinaryTextSegment字段含义变更
if (@available(iOS 16.0, *)) {
    offset = frame.offsetIntoBinaryTextSegment.unsignedLongValue;
    loadAddress = address - offset;
} else {
    loadAddress = frame.offsetIntoBinaryTextSegment.unsignedLongValue;
    offset = address - loadAddress;
}
```

### 2.3 监控效果分析

#### Abort异常分类分布
- **Abort_crash**: 占绝对大头，主要是SIGKILL类型
- **Abort_hang**: 卡顿事件
- **Abort_cpu**: CPU异常（企业版未收到）
- **Abort_disk**: 磁盘I/O异常（企业版未收到）

#### 收益数据
- **监控盲区**: 单日增量最高达287个异常事件
- **用户体验**: 用户角度稳定性事件远大于业务角度
- **重合度**: Normal异常与Abort异常重合度约87%

---

## 3. 腾讯Bugly - iOS ANR监控方案

**原文链接**: https://bugly.tds.qq.com/docs/tutorial/iOS/anr

### 3.1 核心技术方案

#### 检测原理
- **基础机制**: 检查主线程RunLoop执行周期
- **判定标准**: 超过5秒的卡顿标记为可能ANR事件
- **确认逻辑**: 结合App进程退出判定，若退出时仍在长卡顿状态则判定为ANR

#### 堆栈采集策略
- **采样频率**: 60次/秒对主线程堆栈进行采样
- **数据保留**: 保留最近5秒内的堆栈数据
- **精度误差**: ±16ms（由采样间隔导致）

### 3.2 数据分析特色

#### 多维度展示
1. **时间片视图**: 原始采集数据的时间序列展示
   - 格式: `TimeSlice: xx~xx` 表示采集区间
   - 相同时间片的相同调用栈会被合并

2. **堆栈树视图**: 聚合后的调用关系树
   - 每个节点表示对应方法的执行时间
   - 直观显示卡顿方法及其耗时

3. **火焰图**: 堆栈树的图形化表示
   - 可视化性能分析工具

#### 问题聚类机制
- **特征提取**: 使用关键耗时方法中的前三层函数作为特征
- **自动归类**: 将相同特征的ANR个例归类为同一issue
- **全线程支持**: 捕获所有线程堆栈，便于死锁分析

### 3.3 指标体系
- **ANR率** = 退出次数 / 启动次数
- **设备ANR率** = 设备ID去重后的退出次数 / 设备ID去重后的启动次数
- **用户ANR率** = 用户ID去重后的退出次数 / 用户ID去重后的启动次数

---

## 4. 美团 - Hertz 性能监控方案

**原文链接**: https://tech.meituan.com/2016/12/19/hertz.html

### 4.1 Watchdog相关监控能力

#### 卡顿检测原理
- **基础机制**: 检测主线程消息循环执行时间
- **判定策略**: "N次卡顿超过阈值T"的累计判定
- **Watchdog应用**: 可用于监控iOS watchdog问题的0x8badf00d崩溃

#### 监控架构
```mermaid
graph TD
    A[主线程消息循环监控] --> B[计算执行时间]
    B --> C{超过阈值?}
    C -->|是| D[累计卡顿次数]
    C -->|否| E[重置状态]
    D --> F{达到N次?}
    F -->|是| G[触发堆栈采集]
    F -->|否| H[继续监控]
    G --> I[判定为Watchdog事件]
```

### 4.2 技术实现特色

#### 堆栈采集策略
- **采集时机**: 卡顿发生当时进行抓栈，而非事后
- **归类方法**: 按最内层代码归类，匹配简单规则
- **Watchdog识别**: 通过长时间卡顿(如5秒以上)识别潜在的watchdog问题

#### 多阶段应用
1. **开发期**: 实时反馈Watchdog风险，浮层显示性能指标
2. **测试期**: 生成包含Watchdog检测的性能测试报告
3. **线上期**: 通过CAT平台进行Watchdog问题聚类分析

### 4.3 Watchdog监控配置
- **单次长卡顿**: T=5000ms, N=1 (对应潜在Watchdog)
- **频繁卡顿**: T=300ms, N=5 (可能导致累积Watchdog)
- **堆栈归类**: 使用类名前缀匹配规则进行问题分类

### 4.4 与传统方案的差异
- **监控范围**: 不仅监控Watchdog，还包括一般性能问题
- **集成度**: 与开发、测试、线上全流程集成
- **扩展性**: 支持多种数据输出通道和适配器

---

## 5. 方案对比与选择建议

### 5.1 技术方案对比

| 厂商 | 监控目标 | 核心技术 | 数据丰富度 | 实现难度 | 生产可用性 |
|------|----------|----------|------------|----------|------------|
| **字节跳动** | 0x8badf00d崩溃 | RunLoop+多段等待 | 高 | 中 | 高 |
| **货拉拉** | Abort异常 | MetricKit | 中 | 低 | 高 |
| **腾讯Bugly** | ANR事件 | RunLoop+连续采样 | 高 | 中 | 高 |
| **美团Hertz** | 性能+Watchdog | 主线程消息循环 | 中 | 中 | 高 |

### 5.2 选择建议

#### 快速接入方案
- **推荐**: 货拉拉MetricKit方案
- **优势**: 接入简单，Apple官方支持
- **适用**: 快速建立Watchdog监控能力

#### 精准监控方案
- **推荐**: 字节跳动卡死监控方案
- **优势**: 监控精准，误报率低
- **适用**: 对监控精度要求高的场景

#### 专业分析方案
- **推荐**: 腾讯Bugly连续采样方案
- **优势**: 数据分析能力强，可视化好
- **适用**: 需要深度分析的专业APM平台

#### 全流程集成方案
- **推荐**: 美团Hertz方案
- **优势**: 开发测试线上一体化，扩展性强
- **适用**: 需要全面性能监控的团队

### 5.3 实施建议

#### 分阶段实施
1. **第一阶段**: 使用MetricKit快速建立基础监控
2. **第二阶段**: 基于RunLoop实现精准的卡死检测
3. **第三阶段**: 增加连续采样和可视化分析能力
4. **第四阶段**: 集成全流程监控，覆盖开发测试线上

#### 关键技术点
1. **兼容性处理**: 重点关注iOS系统版本差异
2. **性能优化**: 避免监控本身成为性能瓶颈
3. **误报控制**: 通过多种策略减少后台等场景误报
4. **数据分析**: 建立有效的问题聚类和分析机制
5. **全流程集成**: 考虑开发、测试、线上不同阶段的需求

### 5.4 方案组合建议

#### 最佳实践组合
- **基础监控**: MetricKit (快速覆盖) + RunLoop精准检测
- **数据分析**: Bugly连续采样 + 可视化分析
- **流程集成**: Hertz全阶段集成思路
- **问题定位**: 字节跳动的多段等待 + 时间线分析

---

## 6. 技术发展趋势

### 6.1 监控能力演进
- **覆盖范围**: 从Normal崩溃到Abort异常的全覆盖
- **检测精度**: 从简单阈值到多维度精准检测
- **分析深度**: 从单点堆栈到时间线分析
- **集成程度**: 从单一监控到全流程集成

### 6.2 技术标准化
- **Apple官方**: MetricKit能力持续完善
- **行业标准**: 逐步形成统一的监控标准
- **开源方案**: 更多开源实现方案涌现
- **最佳实践**: 各大厂方案逐步融合优化

### 6.3 智能化发展
- **自动分析**: AI辅助的问题根因分析
- **预测能力**: 基于历史数据的风险预测
- **自动修复**: 部分问题的自动恢复机制
- **全链路监控**: 从单点监控到全链路性能分析

---

*iOS Watchdog崩溃监控是移动端稳定性治理的重要组成部分，各大厂的实践方案为行业提供了宝贵的技术参考。选择合适的方案需要综合考虑技术难度、监控精度、实施成本等多个因素。*
