[2025-09-09 11:41:32] [INFO] 脚本开始执行，参数: 
[2025-09-09 11:41:32] [DEBUG] 当前工作目录: /Users/<USER>/Desktop/gongshi
[2025-09-09 11:41:32] [DEBUG] 脚本所在目录: /Users/<USER>/Desktop/gongshi
[2025-09-09 11:41:32] [DEBUG] 当前用户: yanzhen
[2025-09-09 11:41:32] [DEBUG] PATH环境变量: /usr/local/bin:/opt/homebrew/bin:/usr/bin:/bin:/opt/homebrew/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/ruby/bin:/Users/<USER>/fvm/current/bin:/opt/homebrew/opt/node@18/bin:/opt/homebrew/opt/ruby/bin:/Users/<USER>/Documents/command-line-tools/ohpm/bin:/Users/<USER>/Documents/clang-format/clang+llvm-3.7.0-x86_64-apple-darwin/bin:/opt/homebrew/opt/openjdk@11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.cargo/bin:/Applications/iTerm.app/Contents/Resources/utilities:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains:/Users/<USER>/Documents/command-line-tools/bin:/Users/<USER>/Documents/ohpm-repo-5/bin
[2025-09-09 11:41:32] [INFO] 检查依赖工具...
[2025-09-09 11:41:32] [INFO] 依赖工具检查完成
[2025-09-09 11:41:32] [INFO] 测试网络连接...
[2025-09-09 11:41:32] [INFO] 网络连接正常，HTTP状态码: 200
[2025-09-09 11:41:32] [INFO] 查询模式: 今日工时
[2025-09-09 11:41:32] [INFO] 开始构建数据查询请求...
[2025-09-09 11:41:32] [INFO] 开始获取Gateway token...
[2025-09-09 11:41:32] [DEBUG] 请求登录URL: https://zone.haier.net:30931/devp/user-center/account/login
[2025-09-09 11:41:32] [DEBUG] 登录HTTP状态码: 200
[2025-09-09 11:41:32] [DEBUG] 登录响应: {"code":"000000","data":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************.3ZfVbxsqXNKareZaQsy4Qq1d12_h1zKZGv3KctiR9TI","message":"OK","timestamp":*************}
[2025-09-09 11:41:32] [INFO] platform-token获取成功
[2025-09-09 11:41:32] [DEBUG] Gateway HTTP状态码: 200
[2025-09-09 11:41:32] [DEBUG] Gateway响应: {"code":"000000","data":{"token":"f48e5019-64fd-43a9-a3b5-ffef825f6df4","user":{"displayName":"闫振","email":"<EMAIL>","id":"yanzhen","jobNumber":"********","name":"闫振","permissions":[{"code":"ViewDefectDetails","id":"26","name":"查看缺陷详情页"},{"code":"PR_DelComment","id":"117","name":"删除业务评论"},{"code":"BR_ViewDetail","id":"86","name":"查看业务需求详情"},{"code":"ViewTaskDetails","id":"49","name":"查看任务详情页"},{"code":"BR_ViewList","id":"85","name":"查询业务需求列表"},{"code":"ModifyPlanSchedule","id":"17","name":"编辑计划排期"},{"code":"BR_ModifyComment","id":"91","name":"修改业务评论"},{"code":"BR_Del","id":"89","name":"删除业务需求"},{"code":"DeliverableDetails","id":"76","name":"查看交付物"},{"code":"DeleteTask","id":"23","name":"删除任务"},{"code":"AddDefectComment","id":"34","name":"添加缺陷评论"},{"code":"ViewProjectList","id":"1","name":"查看项目列表"},{"code":"PR_ModifyComment","id":"116","name":"修改业务评论"},{"code":"FavoriteProject","id":"5","name":"收藏项目"},{"code":"DeliverableVersionList","id":"78","name":"查看交付物版本"},{"code":"FavoriteRepo","id":"58","name":"收藏仓库"},{"code":"BR_AddComment","id":"90","name":"添加业务需求"},{"code":"ViewTaskList","id":"8","name":"查看任务列表"},{"code":"ModifyPriority","id":"32","name":"编辑优先级"},{"code":"CancelMR","id":"66","name":"取消合并请求"},{"code":"ModifyDefectTitle","id":"28","name":"编辑缺陷标题"},{"code":"ViewTestList","id":"42","name":"查看测试列表"},{"code":"PR_Confirm","id":"109","name":"确认"},{"code":"ModifyVersionAffected","id":"33","name":"编辑影响版本"},{"code":"BR_Modify","id":"88","name":"修改业务需求"},{"code":"ModifyRecurrenceRate","id":"30","name":"编辑复现率"},{"code":"ModifyIncharge","id":"31","name":"编辑负责人"},{"code":"BR_Add","id":"87","name":"创建业务需求"},{"code":"SplitDefectTask","id":"39","name":"拆分缺陷任务"},{"code":"AbnormalInformation","id":"47","name":"查看异常信息明细"},{"code":"ModifyTaskTitle","id":"13","name":"编辑任务标题"},{"code":"ViewDeliverableList","id":"73","name":"查看交付物列表"},{"code":"BR_ExportTable","id":"94","name":"表格导出"},{"code":"StartTask","id":"10","name":"(重新)启动任务"},{"code":"IntegrateArtifact","id":"80","name":"编辑构建清单"},{"code":"CancelDeploy","id":"70","name":"取消部署"},{"code":"ViewBranchDetails","id":"59","name":"查看代码库分支"},{"code":"CreateDeconflict","id":"71","name":"拉取解冲突分支"},{"code":"ViewDefectList","id":"25","name":"查看缺陷列表"},{"code":"ViewTaskDetails","id":"11","name":"查看任务详情页"},{"code":"FavoriteProject","id":"53","name":"收藏项目"},{"code":"AddTaskComment","id":"22","name":"添加任务评论"},{"code":"ArtifactDetails","id":"81","name":"查看构建详情"},{"code":"ModifyTaskDescription","id":"14","name":"编辑任务描述"},{"code":"ViewRepoList","id":"54","name":"查看代码库"},{"code":"BR_DelComment","id":"92","name":"删除业务评论"},{"code":"MergeRequest","id":"65","name":"发起合并请求"},{"code":"Clone","id":"20","name":"克隆"},{"code":"ActivateDefect","id":"27","name":"激活缺陷"},{"code":"AddDefectAttachment","id":"35","name":"添加缺陷附件"},{"code":"CompleteTask","id":"51","name":"完成任务"},{"code":"StartTask","id":"50","name":"(重新)启动任务"},{"code":"ModifyEstimatedWork","id":"18","name":"编辑预估工时"},{"code":"ModifyDefectDescription","id":"29","name":"编辑严重级别"},{"code":"ModifyTaskPriority","id":"16","name":"编辑任务优先级"},{"code":"MoveTask","id":"24","name":"移动任务"},{"code":"ViewWorkbench","id":"48","name":"查看工作台"},{"code":"HistoryTest","id":"45","name":"查看历史提测"},{"code":"ViewDeliverableVerList","id":"79","name":"查看交付物版本列表"},{"code":"CompleteTask","id":"12","name":"完成任务"},{"code":"BR_SplitPR","id":"93","name":"拆分产品需求"},{"code":"RelatedProductDemand","id":"36","name":"关联产品需求"},{"code":"SetRepoAuth","id":"62","name":"设置仓库权限"},{"code":"JumpDatastatisticsDetails","id":"7","name":"跳转数据统计详情"},{"code":"CancelBuild","id":"68","name":"取消打包"},{"code":"ViewProjectOverview","id":"6","name":"查看项目概览"},{"code":"SplitTask","id":"9","name":"拆分任务"},{"code":"SubmitBranch","id":"52","name":"提交分支合并"},{"code":"ProblemAnalysis","id":"38","name":"问题分析"},{"code":"ModifyRelease","id":"64","name":"编辑部署分支"},{"code":"PR_AddComment","id":"115","name":"添加业务需求"},{"code":"BuildDeliverable","id":"77","name":"构建交付物"},{"code":"BuildDeliverable","id":"82","name":"构建交付物"},{"code":"SubmitBranch","id":"19","name":"提交分支合并"},{"code":"PR_ViewDetail","id":"101","name":"查看需求详情"},{"code":"CreateRelease","id":"63","name":"创建部署分支"},{"code":"DeployTag","id":"69","name":"部署代码"},{"code":"ViewDeliverableList","id":"46","name":"查看交付物列表"},{"code":"CreateUnlock","id":"72","name":"拉取解锁分支"},{"code":"AddTaskAttachment","id":"21","name":"添加任务附件"},{"code":"Repo","id":"61","name":"查看仓库配置"},{"code":"ModifyContent","id":"37","name":"编辑内容"},{"code":"ModifyTaskIncharge","id":"15","name":"编辑任务负责人"},{"code":"BuildArtifact","id":"67","name":"打包制品"},{"code":"PR_ViewList","id":"100","name":"查看需求列表"},{"code":"BranchFavoriteRepo","id":"60","name":"收藏仓库"}],"resources":[{"code":"SYN-761","id":"100294","name":"SYN-761","type":"PROJECT"},{"code":"SYN-730","id":"100199","name":"SYN-730","type":"PROJECT"},{"code":"SYN-1046","id":"100892","name":"SYN-1046","type":"PROJECT"},{"code":"SYN-1623","id":"102191","name":"SYN-1623","type":"PROJECT"},{"code":"SYN-654","id":"100092","name":"SYN-654","type":"PROJECT"},{"code":"CODE_BASE2c90e17e86cbf9dd0186da5ce1ce0083","id":"166","name":"CODE_BASE2c90e17e86cbf9dd0186da5ce1ce0083","type":"CODE_BASE"},{"code":"SYN-1568","id":"101936","name":"SYN-1568","type":"PROJECT"},{"code":"SYN-1027","id":"100847","name":"SYN-1027","type":"PROJECT"},{"code":"PRODUCT_LINE_80","id":"100749","name":"PRODUCT_LINE_80","type":"PRODUCT_LINE"},{"code":"CODE_BASE2c90e17e86cbf9dd0186ddae7d6a00eb","id":"202","name":"CODE_BASE2c90e17e86cbf9dd0186ddae7d6a00eb","type":"CODE_BASE"},{"code":"SYN-523","id":"2037","name":"SYN-523","type":"PROJECT"},{"code":"SYN-1737","id":"102522","name":"SYN-1737","type":"PROJECT"},{"code":"SYN-2040","id":"103456","name":"SYN-2040","type":"PROJECT"},{"code":"SYN-2139","id":"103657","name":"SYN-2139","type":"PROJECT"},{"code":"SYN-2436","id":"104204","name":"SYN-2436","type":"PROJECT"},{"code":"SYN-802","id":"100368","name":"SYN-802","type":"PROJECT"},{"code":"SYN-435","id":"1924","name":"SYN-435","type":"PROJECT"},{"code":"SYN-922","id":"100569","name":"SYN-922","type":"PROJECT"},{"code":"SYN-1427","id":"101735","name":"SYN-1427","type":"PROJECT"},{"code":"SYN-860","id":"100449","name":"SYN-860","type":"PROJECT"},{"code":"SYN-2554","id":"104649","name":"SYN-2554","type":"PROJECT"},{"code":"SYN-806","id":"100381","name":"SYN-806","type":"PROJECT"},{"code":"SYN-1856","id":"103154","name":"SYN-1856","type":"PROJECT"},{"code":"SYN-2104","id":"103593","name":"SYN-2104","type":"PROJECT"},{"code":"SYN-1116","id":"101230","name":"SYN-1116","type":"PROJECT"},{"code":"BUSINESS_DEPARTMENT_98","id":"100760","name":"BUSINESS_DEPARTMENT_98","type":"BUSINESS_DEPARTMENT"},{"code":"SYN-2038","id":"103454","name":"SYN-2038","type":"PROJECT"},{"code":"SYN-610","id":"2175","name":"SYN-610","type":"PROJECT"},{"code":"SYN-1437","id":"101748","name":"SYN-1437","type":"PROJECT"},{"code":"SYN-132","id":"1535","name":"SYN-132","type":"PROJECT"},{"code":"SYN-245","id":"1678","name":"SYN-245","type":"PROJECT"},{"code":"SYN-954","id":"100639","name":"SYN-954","type":"PROJECT"},{"code":"SYN-283","id":"1732","name":"SYN-283","type":"PROJECT"},{"code":"SYN-2648","id":"104877","name":"SYN-2648","type":"PROJECT"},{"code":"PRODUCT_LINE_83","id":"100752","name":"PRODUCT_LINE_83","type":"PRODUCT_LINE"},{"code":"SYN-2035","id":"103447","name":"SYN-2035","type":"PROJECT"},{"code":"SYN-2647","id":"104876","name":"SYN-2647","type":"PROJECT"},{"code":"SYN-876","id":"100479","name":"SYN-876","type":"PROJECT"},{"code":"SYN-2294","id":"103846","name":"SYN-2294","type":"PROJECT"},{"code":"SYN-2177","id":"103702","name":"SYN-2177","type":"PROJECT"},{"code":"SYN-466","id":"1957","name":"SYN-466","type":"PROJECT"}],"roles":[{"code":"Developer","id":"5","name":"开发工程师"}]}},"message":"OK","timestamp":1757389292779}
[2025-09-09 11:41:32] [INFO] Gateway token获取成功
[2025-09-09 11:41:32] [INFO] 使用今天日期: 20250909
[2025-09-09 11:41:32] [DEBUG] 查询参数: {
  "componentId": "6d505c54-da61-4ff3-9eaf-9c4015279e0c",
  "componentName": "成员工时分布",
  "configs": [
    {
      "type": "field",
      "config": {
        "fields": [
          {
            "guid": "6307f820-097a-421b-a73c-ca2601f878a9",
            "fid": "2b091b05f4",
            "areaType": "row"
          },
          {
            "guid": "f9512636-3957-4c71-95f0-bff768b91fdd",
            "fid": "b06d0b7b4f",
            "areaType": "column",
            "aggregate": "sum"
          },
          {
            "guid": "90431763-ec65-430d-bf2c-2ac08109b54c",
            "fid": "8e2f3397bc",
            "areaType": "column",
            "aggregate": "sum"
          }
        ]
      },
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73"
    },
    {
      "type": "paging",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "limit": 1000,
        "offset": 0,
        "pagedByAllDim": true
      }
    },
    {
      "type": "beforeAggregateCondition",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "field": {
          "fid": "1f0f97938e"
        },
        "functionalOperator": "in",
        "args": [
          {
            "valueType": "string",
            "value": "APP架构"
          },
          {
            "valueType": "string",
            "value": "Android架构"
          },
          {
            "valueType": "string",
            "value": "iOS架构"
          }
        ]
      }
    },
    {
      "type": "sort",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "sortFields": [
          {
            "sortType": "asc",
            "guid": "f9512636-3957-4c71-95f0-bff768b91fdd",
            "dimValues": [],
            "groupSort": false
          },
          {
            "sortType": "asc",
            "guid": "90431763-ec65-430d-bf2c-2ac08109b54c",
            "dimValues": [],
            "groupSort": false
          }
        ]
      }
    },
    {
      "type": "queryConfig",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "needCount": false,
        "queryCount": false,
        "queryDetail": false
      }
    },
    {
      "type": "placeholder",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "configs": [
          {
            "id": "2f1f74a844",
            "name": "work_date_ph",
            "type": "dateRegion",
            "format": "YYYY-MM-DD",
            "values": [
              "20250909",
              "20250909"
            ]
          }
        ]
      }
    },
    {
      "type": "advancedParam",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "autoInsightParam": {
          "enable": false
        },
        "wordCloudParam": {},
        "summarizeParams": [],
        "trendLineParams": [],
        "forecastParams": [],
        "anomalyDetectionParams": [],
        "clusteringParams": [],
        "groupParam": null,
        "dynamicMetricParam": {}
      }
    },
    {
      "type": "annotationParam",
      "cubeId": "c5ade0cb-29fc-464f-b5a7-36d1e9a64f73",
      "config": {
        "measureThresholdParams": [],
        "inflectionPointParams": []
      }
    }
  ],
  "dataType": "general",
  "reportId": "f37d80b3-e99d-48be-9314-09fc25475fb3"
}
[2025-09-09 11:41:32] [INFO] 开始请求工时数据...
[2025-09-09 11:41:32] [INFO] 数据请求完成，HTTP状态码: HTTPSTATUS:000
[2025-09-09 11:41:32] [DEBUG] 响应内容长度:       21 字符
[2025-09-09 11:41:32] [INFO] 数据请求成功，开始解析数据...
[2025-09-09 11:41:32] [ERROR] 没有返回任何数据
[2025-09-09 11:41:32] [DEBUG] 完整响应: "An error occurred."
[2025-09-09 11:41:32] [ERROR] 将发送数据异常提醒消息
[2025-09-09 11:41:32] [INFO] 开始执行消息发送流程...
[2025-09-09 11:41:32] [INFO] 开始获取飞书访问令牌...
[2025-09-09 11:41:32] [DEBUG] 请求URL: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
[2025-09-09 11:41:32] [DEBUG] APP_ID: cli_a8da644c38fed013
[2025-09-09 11:41:32] [DEBUG] 认证请求数据: {
        "app_id": "cli_a8da644c38fed013",
        "app_secret": "vgdZOTcXh4rbG98XKc8vSdcrRJ3NfMnr"
    }
[2025-09-09 11:41:33] [DEBUG] HTTP状态码: 200
[2025-09-09 11:41:33] [DEBUG] 认证接口响应: {"code":0,"expire":7200,"msg":"ok","tenant_access_token":"t-g10499bFBEJGXVCJMUODUNNIGLT7MRM4NFL5EEW4"}
[2025-09-09 11:41:33] [INFO] 访问令牌获取成功，有效期: 7200秒
[2025-09-09 11:41:33] [DEBUG] Token: t-g10499bFBEJGXVCJMU...
[2025-09-09 11:41:33] [INFO] 准备发送消息，标题: 今日2025-09-09 查询时间: 2025-09-09 11:41:33
[2025-09-09 11:41:33] [INFO] 发送第1条消息: 工时登记异常提醒
[2025-09-09 11:41:33] [DEBUG] 第1条消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}
[2025-09-09 11:41:33] [INFO] 开始发送飞书消息...
[2025-09-09 11:41:33] [DEBUG] 消息类型: text
[2025-09-09 11:41:33] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-09-09 11:41:33] [DEBUG] 消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}...
[2025-09-09 11:41:33] [DEBUG] Token前缀: t-g10499bFBEJGXVCJMU...
[2025-09-09 11:41:33] [DEBUG] 完整请求数据: {
  "content": "{\"text\":\"<at user_id=\\\"all\\\"></at> 工时登记异常提醒\"}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "f94e30d74a3584e1401394932e814f16"
}
[2025-09-09 11:41:33] [DEBUG] HTTP状态码: 200
[2025-09-09 11:41:33] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"@_all 工时登记异常提醒\"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1757389293825","deleted":false,"message_id":"om_x100b44ce5017e1ec0f4ccd3d7b4f0c8","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1757389293825","updated":false},"msg":"success"}
[2025-09-09 11:41:33] [DEBUG] 解析结果 - code字段: '0'
[2025-09-09 11:41:33] [INFO] 消息发送成功，消息ID: om_x100b44ce5017e1ec0f4ccd3d7b4f0c8
[2025-09-09 11:41:33] [INFO] 消息UUID: f94e30d74a3584e1401394932e814f16 (用于防重复)
[2025-09-09 11:41:33] [DEBUG] 业务状态码: 0 (成功)
[2025-09-09 11:41:33] [INFO] 第1条消息发送成功，message_id: om_x100b44ce5017e1ec0f4ccd3d7b4f0c8
[2025-09-09 11:41:33] [INFO] 准备发送第2条消息...
[2025-09-09 11:41:34] [DEBUG] 详细消息内容: {
  "text": "今日2025-09-09 查询时间: 2025-09-09 11:41:33\n\n数据查询异常：没有返回任何工时数据\n请检查数据源或联系管理员\n\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"
}
[2025-09-09 11:41:34] [INFO] 发送第2条消息: 详细工时信息
[2025-09-09 11:41:34] [INFO] 开始发送飞书消息...
[2025-09-09 11:41:34] [DEBUG] 消息类型: text
[2025-09-09 11:41:34] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-09-09 11:41:34] [DEBUG] 消息内容: {
  "text": "今日2025-09-09 查询时间: 2025-09-09 11:41:33\n\n数据查询异常：没有返回任何工时数据\n请检查数据源或联系管理员\n\n[修正工时请移步Z·...
[2025-09-09 11:41:34] [DEBUG] Token前缀: t-g10499bFBEJGXVCJMU...
[2025-09-09 11:41:34] [DEBUG] 完整请求数据: {
  "content": "{\n  \"text\": \"今日2025-09-09 查询时间: 2025-09-09 11:41:33\\n\\n数据查询异常：没有返回任何工时数据\\n请检查数据源或联系管理员\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"\n}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "c418583c1e97e143aba69d6fab78defc"
}
[2025-09-09 11:41:35] [DEBUG] HTTP状态码: 200
[2025-09-09 11:41:35] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"今日2025-09-09 查询时间: 2025-09-09 11:41:33\\n\\n数据查询异常：没有返回任何工时数据\\n请检查数据源或联系管理员\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1757389295481","deleted":false,"message_id":"om_x100b44ce503805480e34308ee14af55","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1757389295481","updated":false},"msg":"success"}
[2025-09-09 11:41:35] [DEBUG] 解析结果 - code字段: '0'
[2025-09-09 11:41:35] [INFO] 消息发送成功，消息ID: om_x100b44ce503805480e34308ee14af55
[2025-09-09 11:41:35] [INFO] 消息UUID: c418583c1e97e143aba69d6fab78defc (用于防重复)
[2025-09-09 11:41:35] [DEBUG] 业务状态码: 0 (成功)
[2025-09-09 11:41:35] [INFO] 第2条消息发送成功，message_id: om_x100b44ce503805480e34308ee14af55
[2025-09-09 11:41:35] [INFO] 没有异常工时用户，跳过紧急短信接口调用
[2025-09-09 11:41:35] [INFO] 所有消息发送完成
