[2025-09-05 19:35:49] [INFO] 脚本开始执行，参数: 
[2025-09-05 19:35:49] [DEBUG] 当前工作目录: /Users/<USER>/Desktop/gongshi
[2025-09-05 19:35:49] [DEBUG] 脚本所在目录: /Users/<USER>/Desktop/gongshi
[2025-09-05 19:35:49] [DEBUG] 当前用户: yanzhen
[2025-09-05 19:35:49] [DEBUG] PATH环境变量: /usr/local/bin:/opt/homebrew/bin:/usr/bin:/bin:/opt/homebrew/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/ruby/bin:/Users/<USER>/fvm/current/bin:/opt/homebrew/opt/node@18/bin:/opt/homebrew/opt/ruby/bin:/Users/<USER>/Documents/command-line-tools/ohpm/bin:/Users/<USER>/Documents/clang-format/clang+llvm-3.7.0-x86_64-apple-darwin/bin:/opt/homebrew/opt/openjdk@11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.cargo/bin:/Applications/iTerm.app/Contents/Resources/utilities:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains:/Users/<USER>/Documents/command-line-tools/bin:/Users/<USER>/Documents/ohpm-repo-5/bin
[2025-09-05 19:35:49] [INFO] 检查依赖工具...
[2025-09-05 19:35:49] [INFO] 依赖工具检查完成
[2025-09-05 19:35:49] [INFO] 测试网络连接...
[2025-09-05 19:35:49] [INFO] 网络连接正常，HTTP状态码: 200
[2025-09-05 19:35:49] [INFO] 查询模式: 今日工时
[2025-09-05 19:35:49] [INFO] 开始构建数据查询请求...
[2025-09-05 19:35:50] [DEBUG] 查询参数: [
  {
    "type": "string/=",
    "value": [
      "iOS架构",
      "APP架构",
      "Android架构"
    ],
    "id": "c343c339",
    "target": [
      "dimension",
      [
        "template-tag",
        "team"
      ]
    ]
  },
  {
    "type": "date/all-options",
    "value": "thisday",
    "id": "2c072968",
    "target": [
      "dimension",
      [
        "template-tag",
        "selectdate"
      ]
    ]
  }
]
[2025-09-05 19:35:50] [DEBUG] 请求URL: https://metabase.haier.net/api/public/dashboard/e48e4958-4d63-43ff-b303-e73ba05d5621/dashcard/432/card/459?parameters=%5B%0A%20%20%7B%0A%20%20%20%20%22type%22%3A%20%22string%2F%3D%22%2C%0A%20%20%20%20%22value%22%3A%20%5B%0A%20%20%20%20%20%20%22iOS%E6%9E%B6%E6%9E%84%22%2C%0A%20%20%20%20%20%20%22APP%E6%9E%B6%E6%9E%84%22%2C%0A%20%20%20%20%20%20%22Android%E6%9E%B6%E6%9E%84%22%0A%20%20%20%20%5D%2C%0A%20%20%20%20%22id%22%3A%20%22c343c339%22%2C%0A%20%20%20%20%22target%22%3A%20%5B%0A%20%20%20%20%20%20%22dimension%22%2C%0A%20%20%20%20%20%20%5B%0A%20%20%20%20%20%20%20%20%22template-tag%22%2C%0A%20%20%20%20%20%20%20%20%22team%22%0A%20%20%20%20%20%20%5D%0A%20%20%20%20%5D%0A%20%20%7D%2C%0A%20%20%7B%0A%20%20%20%20%22type%22%3A%20%22date%2Fall-options%22%2C%0A%20%20%20%20%22value%22%3A%20%22thisday%22%2C%0A%20%20%20%20%22id%22%3A%20%222c072968%22%2C%0A%20%20%20%20%22target%22%3A%20%5B%0A%20%20%20%20%20%20%22dimension%22%2C%0A%20%20%20%20%20%20%5B%0A%20%20%20%20%20%20%20%20%22template-tag%22%2C%0A%20%20%20%20%20%20%20%20%22selectdate%22%0A%20%20%20%20%20%20%5D%0A%20%20%20%20%5D%0A%20%20%7D%0A%5D
[2025-09-05 19:35:50] [INFO] 开始请求工时数据...
[2025-09-05 19:35:50] [INFO] 数据请求完成，HTTP状态码: 400
[2025-09-05 19:35:50] [DEBUG] 响应内容长度:       21 字符
[2025-09-05 19:35:50] [ERROR] 数据请求失败，状态码: 400
[2025-09-05 19:35:50] [ERROR] 响应内容: "An error occurred."
[2025-09-05 19:35:50] [ERROR] 将发送错误提醒消息
[2025-09-05 19:35:50] [INFO] 开始执行消息发送流程...
[2025-09-05 19:35:50] [INFO] 开始获取飞书访问令牌...
[2025-09-05 19:35:50] [DEBUG] 请求URL: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
[2025-09-05 19:35:50] [DEBUG] APP_ID: cli_a8da644c38fed013
[2025-09-05 19:35:50] [DEBUG] 认证请求数据: {
        "app_id": "cli_a8da644c38fed013",
        "app_secret": "vgdZOTcXh4rbG98XKc8vSdcrRJ3NfMnr"
    }
[2025-09-05 19:35:50] [DEBUG] HTTP状态码: 200
[2025-09-05 19:35:50] [DEBUG] 认证接口响应: {"code":0,"expire":7200,"msg":"ok","tenant_access_token":"t-g10495jzRVAHAH7NL557K4VUHTMOPZAST4Y6DDHD"}
[2025-09-05 19:35:50] [INFO] 访问令牌获取成功，有效期: 7200秒
[2025-09-05 19:35:50] [DEBUG] Token: t-g10495jzRVAHAH7NL5...
[2025-09-05 19:35:50] [INFO] 准备发送消息，标题: 今日2025-09-05 查询时间: 2025-09-05 19:35:50
[2025-09-05 19:35:50] [INFO] 发送第1条消息: 工时登记异常提醒
[2025-09-05 19:35:50] [DEBUG] 第1条消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}
[2025-09-05 19:35:50] [INFO] 开始发送飞书消息...
[2025-09-05 19:35:50] [DEBUG] 消息类型: text
[2025-09-05 19:35:50] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-09-05 19:35:50] [DEBUG] 消息内容: {"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}...
[2025-09-05 19:35:50] [DEBUG] Token前缀: t-g10495jzRVAHAH7NL5...
[2025-09-05 19:35:50] [DEBUG] 完整请求数据: {
  "content": "{\"text\":\"<at user_id=\\\"all\\\"></at> 工时登记异常提醒\"}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "cfaec9e7bbdd445f22e2837747d60b8b"
}
[2025-09-05 19:35:51] [DEBUG] HTTP状态码: 200
[2025-09-05 19:35:51] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"@_all 工时登记异常提醒\"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1757072151100","deleted":false,"message_id":"om_x100b4498ffa288bc0f30e7b52e52abc","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1757072151100","updated":false},"msg":"success"}
[2025-09-05 19:35:51] [DEBUG] 解析结果 - code字段: '0'
[2025-09-05 19:35:51] [INFO] 消息发送成功，消息ID: om_x100b4498ffa288bc0f30e7b52e52abc
[2025-09-05 19:35:51] [INFO] 消息UUID: cfaec9e7bbdd445f22e2837747d60b8b (用于防重复)
[2025-09-05 19:35:51] [DEBUG] 业务状态码: 0 (成功)
[2025-09-05 19:35:51] [INFO] 第1条消息发送成功，message_id: om_x100b4498ffa288bc0f30e7b52e52abc
[2025-09-05 19:35:51] [INFO] 准备发送第2条消息...
[2025-09-05 19:35:52] [DEBUG] 详细消息内容: {
  "text": "今日2025-09-05 查询时间: 2025-09-05 19:35:50\n\n数据请求失败，状态码: 400\n请检查网络连接或联系管理员\n\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"
}
[2025-09-05 19:35:52] [INFO] 发送第2条消息: 详细工时信息
[2025-09-05 19:35:52] [INFO] 开始发送飞书消息...
[2025-09-05 19:35:52] [DEBUG] 消息类型: text
[2025-09-05 19:35:52] [DEBUG] 目标群组: oc_81c1fa1c030e8d02dc1e0a1f92059adb
[2025-09-05 19:35:52] [DEBUG] 消息内容: {
  "text": "今日2025-09-05 查询时间: 2025-09-05 19:35:50\n\n数据请求失败，状态码: 400\n请检查网络连接或联系管理员\n\n[修正工时请移步Z·O...
[2025-09-05 19:35:52] [DEBUG] Token前缀: t-g10495jzRVAHAH7NL5...
[2025-09-05 19:35:52] [DEBUG] 完整请求数据: {
  "content": "{\n  \"text\": \"今日2025-09-05 查询时间: 2025-09-05 19:35:50\\n\\n数据请求失败，状态码: 400\\n请检查网络连接或联系管理员\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"\n}",
  "msg_type": "text",
  "receive_id": "oc_81c1fa1c030e8d02dc1e0a1f92059adb",
  "uuid": "60bf036f5e576042d6a3dbea297e2d00"
}
[2025-09-05 19:35:52] [DEBUG] HTTP状态码: 200
[2025-09-05 19:35:52] [DEBUG] 消息发送接口响应: {"code":0,"data":{"body":{"content":"{\"text\":\"今日2025-09-05 查询时间: 2025-09-05 19:35:50\\n\\n数据请求失败，状态码: 400\\n请检查网络连接或联系管理员\\n\\n[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"}"},"chat_id":"oc_81c1fa1c030e8d02dc1e0a1f92059adb","create_time":"1757072152739","deleted":false,"message_id":"om_x100b4498ff4458640e39d5fee35e638","msg_type":"text","sender":{"id":"cli_a8da644c38fed013","id_type":"app_id","sender_type":"app","tenant_key":"1249255f6e4e5758"},"update_time":"1757072152739","updated":false},"msg":"success"}
[2025-09-05 19:35:52] [DEBUG] 解析结果 - code字段: '0'
[2025-09-05 19:35:52] [INFO] 消息发送成功，消息ID: om_x100b4498ff4458640e39d5fee35e638
[2025-09-05 19:35:52] [INFO] 消息UUID: 60bf036f5e576042d6a3dbea297e2d00 (用于防重复)
[2025-09-05 19:35:52] [DEBUG] 业务状态码: 0 (成功)
[2025-09-05 19:35:52] [INFO] 第2条消息发送成功，message_id: om_x100b4498ff4458640e39d5fee35e638
[2025-09-05 19:35:52] [INFO] 没有异常工时用户，跳过紧急短信接口调用
[2025-09-05 19:35:52] [INFO] 所有消息发送完成
