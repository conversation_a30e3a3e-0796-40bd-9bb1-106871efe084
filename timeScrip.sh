#!/bin/bash

# Mac crontab 环境设置
export PATH="/usr/local/bin:/opt/homebrew/bin:/usr/bin:/bin:$PATH"
export LANG="zh_CN.UTF-8"
export LC_ALL="zh_CN.UTF-8"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 飞书应用配置
APP_ID="cli_a8da644c38fed013"
APP_SECRET="vgdZOTcXh4rbG98XKc8vSdcrRJ3NfMnr"
# 群组配置
正式群组
#CHAT_ID="oc_593abde401827c8bd1f0b90f86d71f02"

#  测试群组 自己
CHAT_ID="oc_81c1fa1c030e8d02dc1e0a1f92059adb"

#网器
#CHAT_ID="oc_06f0e06f0b0aab671999cb0f0d74daa5"


# 姓名到工号的映射已在代码中使用case语句实现，避免中文字符编码问题

# 获取脚本所在目录和日志文件路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/timeScrip.log"

# 初始化日志文件（覆盖模式）
> "$LOG_FILE"

# 日志函数 - 同时输出到控制台和日志文件
function log_info {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

function log_error {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

function log_debug {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

# 安全的JSON解析函数 - 专门处理飞书API响应
function safe_json_parse {
    local json_string="$1"
    local jq_expression="$2"
    local default_value="${3:-null}"

    # 尝试解析，忽略所有错误输出
    local result
    result=$(printf '%s' "$json_string" | jq -r "$jq_expression" 2>/dev/null)
    local jq_exit_code=$?

    # 如果jq成功执行且结果不为空且不为null，返回结果
    if [[ $jq_exit_code -eq 0 && -n "$result" && "$result" != "null" ]]; then
        echo "$result"
        return 0
    else
        echo "$default_value"
        return 1
    fi
}

# 检查是否在紧急短信接口调用时间范围内
# 只有查询昨日工时，且在周二至周六早上8:20-8:45之间才调用
function is_urgent_call_time {
    local current_time=$(date '+%H:%M')
    local current_day=$(date '+%u')  # 1=Monday, 7=Sunday

    # 必须是查询昨日工时
    if [[ "$url_parameter" != "past1days" ]]; then
        return 1
    fi

    # 必须是周二到周六早上（2=Tuesday, 6=Saturday）
    if [[ "$current_day" -lt 2 || "$current_day" -gt 6 ]]; then
        return 1
    fi

    # 必须在8:20到8:45之间
    if [[ "$current_time" > "08:19" && "$current_time" < "08:46" ]]; then
        return 0
    fi

    return 1
}

# 调用紧急短信接口
function call_urgent_sms_api {
    local token="$1"
    local message_id="$2"
    local user_id_list="$3"

    log_info "开始调用紧急短信接口..."
    log_debug "消息ID: $message_id"
    log_debug "用户ID列表: $user_id_list"

    # 构建请求数据
    local request_data=$(jq -n --argjson user_list "$user_id_list" '{
        user_id_list: $user_list
    }')

    log_debug "紧急短信接口请求数据: $request_data"

    local max_retries=5
    local retry_count=0

    while [[ $retry_count -lt $max_retries ]]; do
        retry_count=$((retry_count + 1))

        {
            if [[ $retry_count -gt 1 ]]; then
                # 递增延迟策略
                local wait_time=$((retry_count + 1))
                if [[ $retry_count -eq 4 ]]; then wait_time=8; fi
                if [[ $retry_count -eq 5 ]]; then wait_time=10; fi

                log_info "紧急短信接口第 $retry_count 次尝试..."
                log_info "等待 ${wait_time} 秒后重试..."
                sleep $wait_time
            fi
        } >&2

        # 发送请求并等待完整响应（设置30秒超时）
        local response=$(curl -w "HTTPSTATUS:%{http_code}" -s --connect-timeout 10 --max-time 30 -X PATCH "https://open.feishu.cn/open-apis/im/v1/messages/$message_id/urgent_sms?user_id_type=user_id" \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $token" \
            -d "$request_data")

        # 分离HTTP状态码和响应体
        local http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
        local response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')

        {
            log_debug "紧急短信接口HTTP状态码: $http_code"
            log_debug "紧急短信接口响应: $response_body"
        } >&2

        # 检查HTTP状态码
        if [[ "$http_code" != "200" ]]; then
            {
                log_error "紧急短信接口HTTP请求失败，状态码: $http_code"
                log_error "响应内容: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "紧急短信接口已达到最大重试次数($max_retries)，调用失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 检查响应体
        if [[ -z "$response_body" ]]; then
            {
                log_error "紧急电话接口响应体为空"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "紧急电话接口已达到最大重试次数($max_retries)，调用失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 检查是否为有效JSON
        if ! echo "$response_body" | jq . >/dev/null 2>&1; then
            {
                log_error "紧急电话接口响应不是有效的JSON格式"
                log_error "原始响应: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "紧急电话接口已达到最大重试次数($max_retries)，调用失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 解析业务状态码
        local code=$(echo "$response_body" | jq -r '.code // "null"')
        if [[ "$code" == "null" ]]; then
            {
                log_error "紧急电话接口响应中没有找到code字段"
                log_error "完整响应: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "紧急电话接口已达到最大重试次数($max_retries)，调用失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        if [[ "$code" != "0" ]]; then
            local msg=$(echo "$response_body" | jq -r '.msg // "未知错误"')

            # 检查是否是权限问题
            if [[ "$code" == "99991672" ]]; then
                {
                    log_error "紧急电话接口权限不足，错误码: $code"
                    log_error "需要申请权限: im:message.urgent:phone 或 im:message.urgent:phone_send"
                    log_error "权限申请链接: https://open.feishu.cn/app/cli_a8da644c38fed013/auth?q=im:message.urgent:phone,im:message.urgent:phone_send&op_from=openapi&token_type=tenant"
                    log_info "跳过紧急电话接口调用，等待权限开通"
                } >&2
                return 0  # 权限问题不算失败，直接返回成功
            fi

            {
                log_error "紧急电话接口调用失败，错误码: $code"
                log_error "错误信息: $msg"
                log_error "完整响应: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "紧急电话接口已达到最大重试次数($max_retries)，调用失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 成功调用
        { log_info "紧急电话接口调用成功"; } >&2
        return 0
    done

    return 1
}

# 为异常工时用户调用紧急短信接口
function call_urgent_phone_for_abnormal_users {
    local token="$1"
    local message_id="$2"
    local abnormal_users="$3"

    # 检查是否有异常用户
    if [[ -z "$abnormal_users" ]]; then
        log_info "没有异常工时用户，跳过紧急短信接口调用"
        return 0
    fi

    # 检查是否在允许的时间范围内
    if ! is_urgent_call_time; then
        local current_time=$(date '+%u %H:%M')
        log_info "当前时间($current_time)不在紧急短信接口调用时间范围内，跳过调用"
        log_info "调用条件：查询昨日工时 且 周二至周六早上8:20-8:45之间"
        log_info "当前查询模式: $url_parameter, 当前星期: $current_day, 当前时间: $current_time"
        return 0
    fi

    log_info "当前时间在允许范围内，准备调用紧急短信接口"

    # 解析异常用户列表，提取工号
    local user_id_list="["
    local first_user=true

    while IFS= read -r username; do
        # 跳过空行
        [[ -z "$username" ]] && continue

        # 安全地检查用户名映射
        local user_id=""
        case "$username" in
            "吴洋漾") user_id="22011720" ;;
            "闫振") user_id="22005562" ;;
            "逄腾飞") user_id="25031258" ;;
            "刘桐") user_id="01470472" ;;
            "马俊岭") user_id="01433234" ;;
            "郑连乐") user_id="25017579" ;;
            "张文伟") user_id="01500809" ;;
            "陈正烽") user_id="01061068" ;;
            "路标") user_id="20020113" ;;
            "王栋") user_id="01433394" ;;
            "黑建业") user_id="20016533" ;;
            "高雯雯") user_id="25024356" ;;
            "李龙(智家APP)") user_id="01433350" ;;
            "王金龙") user_id="01440585" ;;
            *) user_id="" ;;
        esac

        if [[ -n "$user_id" ]]; then
            if [[ "$first_user" == true ]]; then
                user_id_list+="\"$user_id\""
                first_user=false
            else
                user_id_list+=",\"$user_id\""
            fi
            log_info "添加异常用户: $username (工号: $user_id)"
        else
            log_error "未找到用户 $username 的工号映射"
        fi
    done <<< "$abnormal_users"

    user_id_list+="]"
    # 检查是否有有效的用户ID
    if [[ "$user_id_list" == "[]" ]]; then
        log_info "没有找到有效的用户工号，跳过紧急短信接口调用"
        return 0
    fi

    log_info "准备为以下用户调用紧急短信: $user_id_list"

    # 调用紧急短信接口
    if call_urgent_sms_api "$token" "$message_id" "$user_id_list"; then
        log_info "紧急短信接口调用成功"
    else
        log_error "紧急短信接口调用失败"
    fi
}

# 获取传递的参数
param1="$1"  # 第一个参数

log_info "脚本开始执行，参数: $param1"
log_debug "当前工作目录: $(pwd)"
log_debug "脚本所在目录: $SCRIPT_DIR"
log_debug "当前用户: $(whoami)"
log_debug "PATH环境变量: $PATH"

# 检查必要的工具（crontab友好版本）
function check_dependencies {
    log_info "检查依赖工具..."
    local missing_tools=()

    if ! command -v curl >/dev/null 2>&1; then
        missing_tools+=("curl")
    fi

    if ! command -v jq >/dev/null 2>&1; then
        missing_tools+=("jq")
    fi

    if ! command -v bc >/dev/null 2>&1; then
        missing_tools+=("bc")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "请安装缺少的工具: brew install ${missing_tools[*]}"
        log_error "脚本将尝试继续执行，但可能会失败"
        # 不要直接退出，让脚本尝试继续执行
    else
        log_info "依赖工具检查完成"
    fi
}

# 测试网络连接（crontab友好版本）
function test_network {
    log_info "测试网络连接..."

    # 测试飞书API连接
    local test_response=$(curl -w "HTTPSTATUS:%{http_code}" -s --connect-timeout 5 --max-time 10 "https://open.feishu.cn" 2>/dev/null || echo "HTTPSTATUS:000")
    local test_code=$(echo "$test_response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)

    if [[ "$test_code" == "000" ]]; then
        log_error "无法连接到飞书API服务器，请检查网络连接"
        log_error "脚本将尝试继续执行，但可能会失败"
        # 不要直接退出，让脚本尝试继续执行
    else
        log_info "网络连接正常，HTTP状态码: $test_code"
    fi
}

# 执行依赖检查
check_dependencies
test_network

# 根据 param1 的值设置 URL 参数
if [[ "$param1" == "2" ]]; then
    url_parameter="past1days"
    log_info "查询模式: 昨日工时"
else
    url_parameter="thisday"
    log_info "查询模式: 今日工时"
fi

# 获取飞书 tenant_access_token
function get_tenant_access_token {
    # 将所有日志输出到stderr，避免污染函数返回值
    {
        log_info "开始获取飞书访问令牌..."
        log_debug "请求URL: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        log_debug "APP_ID: $APP_ID"
    } >&2

    # 构建请求数据
    local auth_data="{
        \"app_id\": \"$APP_ID\",
        \"app_secret\": \"$APP_SECRET\"
    }"

    { log_debug "认证请求数据: $auth_data"; } >&2

    # 发送请求并获取HTTP状态码
    local response=$(curl -w "HTTPSTATUS:%{http_code}" -s -X POST 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' \
        -H 'Content-Type: application/json' \
        -d "$auth_data")

    # 分离HTTP状态码和响应体
    local http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')

    {
        log_debug "HTTP状态码: $http_code"
        log_debug "认证接口响应: $response_body"
    } >&2

    # 检查HTTP状态码
    if [[ "$http_code" != "200" ]]; then
        {
            log_error "认证HTTP请求失败，状态码: $http_code"
            log_error "响应内容: $response_body"
        } >&2
        exit 1
    fi

    # 检查响应体是否为空
    if [[ -z "$response_body" ]]; then
        { log_error "认证响应体为空"; } >&2
        exit 1
    fi

    # 检查是否为有效JSON
    if ! echo "$response_body" | jq . >/dev/null 2>&1; then
        {
            log_error "认证响应不是有效的JSON格式"
            log_error "原始响应: $response_body"
        } >&2
        exit 1
    fi

    # 解析业务状态码
    local code=$(safe_json_parse "$response_body" '.code' 'null')
    if [[ "$code" == "null" ]]; then
        {
            log_error "认证响应中没有找到code字段"
            log_error "完整响应: $response_body"
        } >&2
        exit 1
    fi

    if [[ "$code" != "0" ]]; then
        local msg=$(safe_json_parse "$response_body" '.msg' '未知错误')
        {
            log_error "获取 token 失败，错误码: $code"
            log_error "错误信息: $msg"
            log_error "完整响应: $response_body"
        } >&2
        exit 1
    fi

    local token=$(safe_json_parse "$response_body" '.tenant_access_token' 'null')
    if [[ "$token" == "null" || -z "$token" ]]; then
        {
            log_error "响应中没有找到有效的token"
            log_error "完整响应: $response_body"
        } >&2
        exit 1
    fi

    local expire=$(safe_json_parse "$response_body" '.expire' 'unknown')
    {
        log_info "访问令牌获取成功，有效期: ${expire}秒"
        log_debug "Token: ${token:0:20}..."
    } >&2

    # 只输出token到stdout
    echo "$token"
}

# 发送飞书消息（带重试机制）
function send_feishu_message_with_retry {
    local token="$1"
    local content="$2"
    local msg_type="$3"
    local max_retries=5
    local retry_count=0

    while [[ $retry_count -lt $max_retries ]]; do
        retry_count=$((retry_count + 1))

        {
            if [[ $retry_count -gt 1 ]]; then
                # 递增延迟：第2次等待3秒，第3次等待5秒，第4次等待8秒，第5次等待10秒
                local wait_time=$((retry_count + 1))
                if [[ $retry_count -eq 4 ]]; then wait_time=8; fi
                if [[ $retry_count -eq 5 ]]; then wait_time=10; fi

                log_info "第 $retry_count 次尝试发送消息..."
                log_info "等待 ${wait_time} 秒后重试，避免频率限制..."
                sleep $wait_time
            else
                log_info "开始发送飞书消息..."
            fi

            log_debug "消息类型: $msg_type"
            log_debug "目标群组: $CHAT_ID"
            log_debug "消息内容: ${content:0:100}..."
            log_debug "Token前缀: ${token:0:20}..."
        } >&2

        # 使用jq构建请求数据，确保JSON格式正确
        # 生成唯一UUID避免重复发送（Mac兼容）
        if command -v md5sum >/dev/null 2>&1; then
            local uuid=$(date +%s%N | md5sum | cut -c1-32)
        else
            local uuid=$(date +%s%N | md5 | cut -c1-32)
        fi
        local request_data=$(jq -n \
            --arg content "$content" \
            --arg msg_type "$msg_type" \
            --arg receive_id "$CHAT_ID" \
            --arg uuid "$uuid" \
            '{
                content: $content,
                msg_type: $msg_type,
                receive_id: $receive_id,
                uuid: $uuid
            }')

        { log_debug "完整请求数据: $request_data"; } >&2

        # 发送请求并等待完整响应（设置30秒超时）
        local response=$(curl -w "HTTPSTATUS:%{http_code}" -s --connect-timeout 10 --max-time 30 -X POST "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id" \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $token" \
            -d "$request_data")

        # 分离HTTP状态码和响应体
        local http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
        local response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')

        {
            log_debug "HTTP状态码: $http_code"
            log_debug "消息发送接口响应: $response_body"
        } >&2

        # 检查HTTP状态码
        if [[ "$http_code" != "200" ]]; then
            {
                log_error "HTTP请求失败，状态码: $http_code"
                log_error "响应内容: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    local next_wait=$((retry_count + 2))
                    if [[ $((retry_count + 1)) -eq 4 ]]; then next_wait=8; fi
                    if [[ $((retry_count + 1)) -eq 5 ]]; then next_wait=10; fi
                    log_info "将在 ${next_wait} 秒后重试..."
                else
                    log_error "已达到最大重试次数($max_retries)，发送失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 检查响应体是否为空
        if [[ -z "$response_body" ]]; then
            {
                log_error "响应体为空"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "已达到最大重试次数($max_retries)，发送失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 直接尝试解析业务状态码，不验证JSON格式
        local code=$(safe_json_parse "$response_body" '.code' 'null')

        {
            log_debug "解析结果 - code字段: '$code'"
        } >&2

        # 如果无法解析出code字段，说明响应格式有问题
        if [[ "$code" == "null" ]]; then
            {
                log_error "无法从响应中解析code字段"
                log_error "原始响应前100字符: ${response_body:0:100}..."
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "已达到最大重试次数($max_retries)，发送失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 检查业务状态码
        if [[ "$code" != "0" ]]; then
            local msg=$(safe_json_parse "$response_body" '.msg' '未知错误')
            {
                log_error "发送消息失败，错误码: $code"
                log_error "错误信息: $msg"
                log_error "完整响应: $response_body"
                if [[ $retry_count -lt $max_retries ]]; then
                    log_info "将在2秒后重试..."
                else
                    log_error "已达到最大重试次数($max_retries)，发送失败"
                fi
            } >&2
            if [[ $retry_count -lt $max_retries ]]; then
                continue
            else
                return 1
            fi
        fi

        # 成功发送，提取message_id并返回
        local message_id=$(safe_json_parse "$response_body" '.data.message_id' 'unknown')
        {
            log_info "消息发送成功，消息ID: $message_id"
            log_info "消息UUID: $uuid (用于防重复)"
            log_debug "业务状态码: $code (成功)"
        } >&2

        # 将message_id输出到stdout供调用者获取
        echo "$message_id"
        return 0
    done

    # 如果到这里说明所有重试都失败了
    return 1
}

# 封装方法 B - 使用新的飞书接口
function method_B {
    local messages="$1"
    local abnormal_users="$2"

    log_info "开始执行消息发送流程..."

    # 获取 token
    local token=$(get_tenant_access_token)
    if [[ -z "$token" ]]; then
        log_error "无法获取访问令牌，脚本无法继续执行"
        return 1
    fi

    # 获取当前日期
    current_date=$(date +"%Y-%m-%d")

    # 判断是否为昨日并获取昨天的日期（Mac兼容）
    if [[ "$url_parameter" == "past1days" ]]; then
        # 尝试Mac格式，如果失败则使用GNU格式
        if current_date2=$(date -v -1d +"%Y-%m-%d" 2>/dev/null); then
            today="昨日${current_date2}"
        elif current_date2=$(date -d "yesterday" +"%Y-%m-%d" 2>/dev/null); then
            today="昨日${current_date2}"
        else
            # 如果都失败，使用当前日期
            today="昨日${current_date}"
        fi
    else
        today="今日${current_date}"
    fi

    # 获取当前时间
    current_time=$(date +"%H:%M:%S")
    today="${today} 查询时间: ${current_date} ${current_time}"

    log_info "准备发送消息，标题: $today"

    # 发送提醒消息（第1条消息）
    log_info "发送第1条消息: 工时登记异常提醒"
    # 使用与成功示例完全相同的格式
    local alert_content='{"text":"<at user_id=\"all\"></at> 工时登记异常提醒"}'
    log_debug "第1条消息内容: $alert_content"

    # 发送第1条消息并获取message_id
    local first_message_id
    first_message_id=$(send_feishu_message_with_retry "$token" "$alert_content" "text")
    local first_send_result=$?

    if [[ $first_send_result -ne 0 ]]; then
        log_error "第1条消息发送失败，已重试5次，停止执行"
        return 1
    fi

    log_info "第1条消息发送成功，message_id: $first_message_id"
    log_info "准备发送第2条消息..."

    # 添加短暂延迟，避免消息发送过快
    sleep 1

    # 构建详细消息内容（第2条消息）
    # 将\n转换为实际换行符
    local processed_messages="${messages//\\n/$'\n'}"
    local detail_text="${today}

${processed_messages}

[修正工时请移步Z·ONE](https://zone.haier.net:8080/#/home/<USER>"

    # 使用jq构建content，确保JSON格式正确
    local detail_content=$(jq -n --arg text "$detail_text" '{"text": $text}')

    log_debug "详细消息内容: $detail_content"

    # 发送第2条消息并获取message_id
    log_info "发送第2条消息: 详细工时信息"
    local second_message_id
    second_message_id=$(send_feishu_message_with_retry "$token" "$detail_content" "text")
    local second_send_result=$?

    if [[ $second_send_result -ne 0 ]]; then
        log_error "第2条消息发送失败，已重试5次"
        return 1
    fi

    log_info "第2条消息发送成功，message_id: $second_message_id"

    # 调用紧急短信接口（使用第1条消息的ID）
    call_urgent_phone_for_abnormal_users "$token" "$first_message_id" "$abnormal_users"

    log_info "所有消息发送完成"
}

# 基础 URL
base_url="https://metabase.haier.net/api/public/dashboard/e48e4958-4d63-43ff-b303-e73ba05d5621/dashcard/432/card/459"

log_info "开始构建数据查询请求..."

json_data=$(jq -n --arg param "$url_parameter" '[
    {
        "type": "string/=",
        "value": ["iOS架构","APP架构","Android架构"],
        "id": "c343c339",
        "target": ["dimension", ["template-tag", "team"]]
    },
    {
        "type": "date/all-options",
        "value": $param,
        "id": "2c072968",
        "target": ["dimension", ["template-tag", "selectdate"]]
    }
]')

log_debug "查询参数: $json_data"

# URL 编码
encoded_parameters=$(printf "%s" "$json_data" | jq -sRr @uri)

# 构造完整的 URL
url="$base_url?parameters=$encoded_parameters"
log_debug "请求URL: $url"

log_info "开始请求工时数据..."

# 使用绝对路径创建临时文件
temp_file="$SCRIPT_DIR/temp_response.json"

# 发起 GET 请求并解析 JSON 响应
response=$(curl -s -w "%{http_code}" -o "$temp_file" --location -g --request GET "$url" \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: metabase.haier.net' \
--header 'Connection: keep-alive' \
--header 'Cookie: metabase.DEVICE=8688dfb7-ddfd-4f8b-92f4-55ad150df8ac')

status_code=$response
log_info "数据请求完成，HTTP状态码: $status_code"

# 读取响应内容
response_content=$(<"$temp_file")
log_debug "响应内容长度: $(echo "$response_content" | wc -c) 字符"

# 检查状态码
if [[ "$status_code" -ne 200 && "$status_code" -ne 202 ]]; then
    log_error "数据请求失败，状态码: $status_code"
    log_error "响应内容: $response_content"
    log_error "将发送错误提醒消息"
    # 发送错误提醒而不是直接退出
    method_B "数据请求失败，状态码: $status_code\n请检查网络连接或联系管理员" ""
    exit 1
fi

log_info "数据请求成功，开始解析数据..."

# 使用 jq 工具解析 JSON 数据
rows=$(echo "$response_content" | jq -r '.data.rows')

# 检查 rows 是否为空
if [[ -z "$rows" || "$rows" == "null" ]]; then
    log_error "没有返回任何数据"
    log_debug "完整响应: $response_content"
    log_error "将发送数据异常提醒消息"
    # 发送数据异常提醒而不是直接退出
    method_B "数据查询异常：没有返回任何工时数据\n请检查数据源或联系管理员" ""
    exit 1
fi

# 统计总记录数
total_rows=$(echo "$rows" | jq '. | length')
log_info "获取到 $total_rows 条工时记录"

# 用于拼接消息的变量
messages=""
abnormal_count=0
abnormal_users=""

# 遍历每一行
log_info "开始分析工时数据..."
for row in $(echo "$rows" | jq -c '.[]'); do
    name=$(echo "$row" | jq -r '.[0]')
    value=$(echo "$row" | jq -r '.[2]')

    # 检查值是否小于 8
    if (( $(echo "$value < 8" | bc -l) )); then
        messages+="$name 实际工时为：【${value} 小时】\n"
        abnormal_count=$((abnormal_count + 1))
        # 收集异常用户名称
        if [[ -n "$abnormal_users" ]]; then
            abnormal_users+=$'\n'"$name"
        else
            abnormal_users="$name"
        fi
        log_info "发现异常工时: $name = $value 小时"
    fi
done

log_info "数据分析完成，发现 $abnormal_count 条异常工时记录"

# 发送消息
if [[ -n "$messages" ]]; then
    log_info "准备发送异常工时提醒消息"
    log_debug "消息内容: $messages"
    log_debug "异常用户列表: $abnormal_users"
    method_B "$messages" "$abnormal_users"
    log_info "脚本执行完成 - 发现异常工时"
else
    log_info "没有发现异常工时，发送正常状态消息"
    messages="无异常工时记录"
    method_B "$messages" ""
    log_info "脚本执行完成 - 工时正常"
fi

# 清理临时文件
if [[ -f "$temp_file" ]]; then
    rm "$temp_file"
    log_debug "已清理临时文件"
fi
