# RunLoop 卡顿监控流程图

## 整体架构流程

```mermaid
graph TD
    A[应用启动] --> B[初始化TMWatchDog]
    B --> C[调用beginMonitor开始监控]
    C --> D[创建CFRunLoopObserver观察者]
    D --> E[将观察者添加到主线程RunLoop]
    E --> F[创建子线程监控]
    F --> G[子线程while循环监控]
    
    G --> H{等待信号量超时?}
    H -->|未超时| I[收到RunLoop状态变化信号]
    H -->|超时3秒| J[检查当前RunLoop状态]
    
    I --> K[更新runLoopActivity状态]
    K --> L[发送信号量]
    L --> G
    
    J --> M{状态是BeforeSources或AfterWaiting?}
    M -->|是| N[检测到主线程卡顿]
    M -->|否| G
    
    N --> O[使用UPCrash上报堆栈]
    O --> P[记录卡顿日志]
    P --> G
    
    style N fill:#ff6b6b
    style O fill:#4ecdc4
    style P fill:#45b7d1
```

## RunLoop 六个状态详解

```mermaid
graph LR
    A[kCFRunLoopEntry<br/>即将进入Loop] --> B[kCFRunLoopBeforeTimers<br/>即将处理Timers]
    B --> C[kCFRunLoopBeforeSources<br/>即将处理Sources]
    C --> D[kCFRunLoopBeforeWaiting<br/>即将进入休眠]
    D --> E[kCFRunLoopAfterWaiting<br/>刚从休眠中唤醒]
    E --> F[kCFRunLoopExit<br/>即将退出Loop]
    
    C --> G[处理事件]
    G --> H{还有事件?}
    H -->|有| C
    H -->|无| D
    
    E --> I[处理唤醒事件]
    I --> C
    
    style C fill:#ff9999
    style E fill:#ff9999
    style G fill:#ffcc99
```

## 卡顿检测核心逻辑

```mermaid
graph TD
    A[RunLoop状态变化] --> B[runLoopObserverCallBack回调]
    B --> C[更新runLoopActivity状态]
    C --> D[发送信号量dispatch_semaphore_signal]
    
    E[子线程监控循环] --> F[等待信号量dispatch_semaphore_wait]
    F --> G{等待结果}
    G -->|成功接收到信号| H[重置超时计数]
    G -->|等待超时3秒| I[检查RunLoop状态]
    
    H --> E
    
    I --> J{当前状态是否为<br/>BeforeSources或AfterWaiting?}
    J -->|是| K[🚨 检测到卡顿]
    J -->|否| L[继续监控]
    
    K --> M[获取主线程堆栈信息]
    M --> N[调用UPCrash上报]
    N --> O[生成卡顿报告]
    O --> L
    
    L --> E
    
    style K fill:#ff4757
    style M fill:#ffa502
    style N fill:#2ed573
    style O fill:#1e90ff
```

## 堆栈信息获取流程

```mermaid
graph TD
    A[检测到卡顿] --> B[调用UPCrash接口]
    B --> C[reportUserException方法]
    C --> D[传入参数]
    
    D --> E[name: TM_TEST]
    D --> F[reason: TM_WATCH_DOG]
    D --> G[stackTrace: nil<br/>由UPCrash自动获取]
    
    E --> H[UPCrash处理]
    F --> H
    G --> H
    
    H --> I[获取当前线程堆栈]
    I --> J[生成崩溃报告]
    J --> K[存储到沙盒Document目录]
    K --> L[上传到服务器]
    
    style A fill:#ff6b6b
    style C fill:#4ecdc4
    style H fill:#45b7d1
    style L fill:#26de81
```

## 关键代码实现流程

```mermaid
sequenceDiagram
    participant App as 主线程
    participant Observer as RunLoop观察者
    participant Monitor as 监控子线程
    participant UPCrash as UPCrash组件
    
    App->>Observer: 创建CFRunLoopObserver
    App->>Observer: 添加到主线程RunLoop
    App->>Monitor: 创建监控子线程
    
    loop RunLoop运行
        App->>Observer: RunLoop状态变化
        Observer->>Monitor: 发送信号量
        Monitor->>Monitor: 重置超时计数
    end
    
    Note over Monitor: 3秒内未收到信号量
    Monitor->>Monitor: 检查RunLoop状态
    
    alt 状态为BeforeSources或AfterWaiting
        Monitor->>UPCrash: reportUserException
        UPCrash->>UPCrash: 获取堆栈信息
        UPCrash->>UPCrash: 生成崩溃报告
        UPCrash-->>Monitor: 上报完成
    else 其他状态
        Monitor->>Monitor: 继续监控
    end
```

## 监控配置参数

| 参数 | 值 | 说明 |
|------|----|----- |
| 超时阈值 | 3秒 | 超过3秒未收到RunLoop状态变化信号 |
| 监控状态 | BeforeSources, AfterWaiting | 这两个状态间隔时间能检测卡顿 |
| 信号量类型 | dispatch_semaphore_t | 保证线程同步 |
| 观察者模式 | kCFRunLoopAllActivities | 监控所有RunLoop活动 |
| 运行模式 | kCFRunLoopCommonModes | 通用模式，覆盖更多场景 |

## 核心优势

1. **实时监控**: 基于RunLoop机制，实时感知主线程状态
2. **精确检测**: 通过BeforeSources和AfterWaiting状态精确判断卡顿
3. **自动上报**: 集成UPCrash，自动获取堆栈并上报
4. **低开销**: 仅在卡顿时进行堆栈采集，平时开销极小
5. **可控制**: 支持动态开启/关闭监控功能

---

*此流程图基于TMWatchDog实现方案，通过RunLoop状态监控实现主线程卡顿检测，并结合UPCrash进行堆栈信息采集和上报。*
