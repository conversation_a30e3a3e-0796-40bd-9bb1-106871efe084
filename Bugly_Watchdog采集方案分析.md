# Bugly Watchdog 采集方案总结

## 1. 核心检测原理

### 1.1 基础机制
Bugly通过**检查主线程RunLoop执行周期**来判定主线程是否发生卡顿。

```
检测流程：监控RunLoop → 判断卡顿 → 采样堆栈 → 进程退出判定 → ANR确认
```

### 1.2 ANR判定逻辑
```mermaid
graph TD
    A[监控主线程RunLoop] --> B{卡顿超过5秒?}
    B -->|否| A
    B -->|是| C[标记为可能ANR事件]
    C --> D[继续监控进程状态]
    D --> E{进程退出时仍在卡顿?}
    E -->|是| F[判定为ANR]
    E -->|否| G[非ANR退出]
    F --> H[上报ANR事件]

    style F fill:#ff6b6b
    style H fill:#4ecdc4
```

## 2. 技术实现方案

### 2.1 监控阈值
- **默认阈值**：5秒
- **判定标准**：超过5秒的卡顿标记为可能ANR事件

### 2.2 堆栈采集策略
```mermaid
graph LR
    A[60Hz采样频率] --> B[周期性采集主线程堆栈]
    B --> C[发生卡顿时保留最近5秒数据]
    C --> D[生成时间片/堆栈树/火焰图]

    style A fill:#ffa502
    style D fill:#2ed573
```

**核心特点**：
- **采样频率**：60次/秒
- **数据保留**：最近5秒内的堆栈数据
- **精度误差**：±16ms（由于采样间隔导致）

### 2.3 数据采集内容
1. **主线程堆栈**：周期性采集的主线程调用栈
2. **全线程堆栈**：判定疑似ANR时捕获所有线程的调用栈
3. **时间信息**：每次采集的时间戳和持续时间

## 3. 数据分析与展示

### 3.1 调用栈特征提取
```mermaid
graph TD
    A[原始调用栈] --> B[构建调用树]
    B --> C[找到耗时最大路径]
    C --> D[提取前三层函数作为特征]
    D --> E[用于问题聚类]

    style C fill:#ffa502
    style E fill:#2ed573
```

**特征提取规则**：
- 将相邻相同的帧进行合并，构建调用树
- 找到耗时较大的一条路径作为关键耗时方法
- 提取这条路径中的前三层函数作为特征

### 3.2 多维度数据展示
1. **时间片视图**：
   - 原始采集数据的时间序列展示
   - 格式：`TimeSlice: xx~xx` 表示采集区间
   - 相同时间片的相同调用栈会被合并

2. **堆栈树视图**：
   - 从栈底依次往上聚合生成的调用树
   - 每个节点表示对应方法的执行时间
   - 直观显示卡顿方法及其耗时

3. **火焰图**：
   - 堆栈树的图形化表示
   - 可视化性能分析工具

### 3.3 问题聚类机制
- 使用关键耗时方法中的前三层函数作为特征
- 将相同特征的ANR个例归类为同一个issue
- 便于问题跟踪和批量修复

## 4. 全线程堆栈分析

### 4.1 采集时机
在判定疑似ANR时捕获所有线程的调用栈，包括主线程。

### 4.2 应用场景
- **死锁检测**：查看是否有线程持有相同的锁
- **锁竞争分析**：分析长时间的锁等待问题
- **线程状态分析**：了解所有线程的执行状态

## 5. ANR指标体系

### 5.1 核心指标
- **ANR率** = 退出次数 / 启动次数
- **设备ANR率** = 设备ID去重后的退出次数 / 设备ID去重后的启动次数
- **用户ANR率** = 用户ID去重后的退出次数 / 用户ID去重后的启动次数

### 5.2 特殊处理
- **无堆栈问题**：没有开启堆栈采样的个例会归类到"无堆栈问题"
- **SIGKILL处理**：将已知退出排除后的退出当作SIGKILL处理

## 6. 技术特点总结

| 维度 | Bugly方案特点 |
|------|---------------|
| **监控方式** | 基于RunLoop + 60Hz连续采样 |
| **检测阈值** | 默认5秒，相对保守 |
| **数据精度** | ±16ms误差，由采样频率决定 |
| **存储策略** | 持续存储5秒数据，开销较大 |
| **分析能力** | 提供时间片、堆栈树、火焰图多种视图 |
| **问题聚类** | 基于前三层函数特征自动聚类 |
| **全线程支持** | 支持全线程堆栈采集，便于死锁分析 |
| **可视化** | 丰富的图形化分析工具 |

---

*Bugly采用连续采样 + 多维度分析的方案，在数据丰富度和分析能力方面表现突出，但相应的性能开销也较大。*
